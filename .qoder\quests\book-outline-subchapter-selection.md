# Book Outline Sub-Chapter Selection Design

## Overview

This design document outlines the implementation of interactive sub-chapter selection functionality in the Book Outline section. The feature will provide visual feedback when users click on sub-chapters, implementing a single-selection model with persistent highlighting that integrates seamlessly with the existing glass-morphism design theme.

## Technology Stack & Dependencies

- **Frontend**: React 18.3.1 with TypeScript 5.6.3
- **Styling**: Tailwind CSS 3.4.14 with glass-morphism theme
- **UI Components**: Radix UI Accordion component
- **State Management**: React useState hooks
- **Design System**: Glass-morphism with backdrop blur effects

## Component Architecture

### Current Structure Analysis

The Book Outline section is implemented in the `OutlineGenerator` component with the following hierarchy:

```mermaid
graph TD
    A[OutlineGenerator] --> B[Accordion Container]
    B --> C[AccordionItem per Chapter]
    C --> D[AccordionTrigger - Chapter Title]
    C --> E[AccordionContent - Sub-chapters]
    E --> F[Generate All Sub-Chapters Button]
    E --> G[Sub-chapter List ul]
    G --> H[Sub-chapter Item li]
    H --> I[Sub-chapter Text]
    H --> J[Status Icons]
```

### Enhanced Component Architecture

The enhanced architecture will add selection state management and visual feedback:

```mermaid
graph TD
    A[OutlineGenerator] --> B[Selection State Management]
    B --> C[selectedSubChapter: SubChapterRef | null]
    B --> D[handleSubChapterClick handler]
    
    A --> E[Accordion Container]
    E --> F[AccordionItem per Chapter]
    F --> G[AccordionContent - Sub-chapters]
    G --> H[Sub-chapter List ul]
    H --> I[Enhanced Sub-chapter Item li]
    I --> J[Dynamic CSS Classes]
    I --> K[Click Handler]
    I --> L[Visual Feedback States]
```

## Data Models & State Management

### Sub-Chapter Reference Model

```typescript
interface SubChapterRef {
  chapterIndex: number;
  subChapterIndex: number;
  chapterTitle: string;
  subChapterTitle: string;
}
```

### Selection State Model

```typescript
interface SubChapterSelectionState {
  selectedSubChapter: SubChapterRef | null;
}
```

### State Management Pattern

The selection state will be managed locally within the `OutlineGenerator` component using React's `useState` hook:

```typescript
const [selectedSubChapter, setSelectedSubChapter] = useState<SubChapterRef | null>(null);
```

## Visual Design & Styling Strategy

### Glass-Morphism Integration

The selection highlighting will integrate with the existing glass-morphism design system:

#### Color Palette
- **Default State**: `hover:bg-background` (existing hover state)
- **Selected State**: `bg-accent/20 border-accent/30`
- **Selected Hover**: `bg-accent/25 border-accent/40`

#### Visual Effects
- **Backdrop Blur**: Maintain existing blur consistency
- **Border Treatment**: Subtle accent border for selected items
- **Transition Animation**: Smooth 300ms transition for all states
- **Glass Effect**: Enhanced glass-card appearance for selected items

### CSS Class Strategy

```css
/* Base sub-chapter item styles */
.subchapter-item {
  @apply flex justify-between items-center p-2 rounded group transition-all duration-300;
  @apply hover:bg-background cursor-pointer;
}

/* Selected state */
.subchapter-item-selected {
  @apply bg-accent/20 border border-accent/30;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

/* Selected hover state */
.subchapter-item-selected:hover {
  @apply bg-accent/25 border-accent/40;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Disabled state preservation */
.subchapter-item-disabled {
  @apply opacity-50 cursor-not-allowed;
  pointer-events: none;
}
```

## Business Logic Implementation

### Selection Logic Flow

```mermaid
flowchart TD
    A[User Clicks Sub-chapter] --> B{Is Sub-chapter Enabled?}
    B -->|No| C[Show Sequential Generation Toast]
    B -->|Yes| D{Is Same as Currently Selected?}
    D -->|Yes| E[Clear Selection - setSelectedSubChapter null]
    D -->|No| F[Update Selection State]
    F --> G[Create SubChapterRef Object]
    G --> H[setSelectedSubChapter with new ref]
    H --> I[Apply Visual Feedback]
    I --> J[Call Existing onChapterSelect]
```

### Click Handler Implementation

```typescript
const handleSubChapterClick = (
  chapter: Chapter,
  subchapter: string,
  chapterIndex: number,
  subIndex: number,
  isEnabled: boolean
) => {
  if (!isEnabled) {
    // Preserve existing sequential generation toast
    toast({
      title: "Sequential generation required",
      description: "Please generate content for chapters in sequential order.",
      variant: "destructive"
    });
    return;
  }

  const newSelection: SubChapterRef = {
    chapterIndex,
    subChapterIndex: subIndex,
    chapterTitle: chapter.title,
    subChapterTitle: subchapter
  };

  // Toggle selection: if same item clicked, deselect
  if (selectedSubChapter && 
      selectedSubChapter.chapterIndex === chapterIndex && 
      selectedSubChapter.subChapterIndex === subIndex) {
    setSelectedSubChapter(null);
  } else {
    setSelectedSubChapter(newSelection);
  }

  // Call existing chapter selection logic
  onChapterSelect(chapter.title, subchapter, chapterIndex + 1, chapterIndex, subIndex);
};
```

### Selection State Utility Functions

```typescript
const isSubChapterSelected = (chapterIndex: number, subIndex: number): boolean => {
  return selectedSubChapter?.chapterIndex === chapterIndex && 
         selectedSubChapter?.subChapterIndex === subIndex;
};

const getSubChapterClasses = (chapterIndex: number, subIndex: number, isEnabled: boolean): string => {
  const baseClasses = "flex justify-between items-center p-2 rounded group transition-all duration-300";
  
  if (!isEnabled) {
    return `${baseClasses} opacity-50 cursor-not-allowed`;
  }
  
  const isSelected = isSubChapterSelected(chapterIndex, subIndex);
  
  if (isSelected) {
    return `${baseClasses} bg-accent/20 border border-accent/30 cursor-pointer hover:bg-accent/25 hover:border-accent/40`;
  }
  
  return `${baseClasses} cursor-pointer hover:bg-background`;
};
```

## Component Integration Strategy

### Enhanced Sub-Chapter Rendering

The existing sub-chapter rendering logic will be enhanced while preserving all existing functionality:

```typescript
// Enhanced sub-chapter list item rendering
<li 
  key={`subchapter-${chapterIndex}-${subIndex}`}
  className={getSubChapterClasses(chapterIndex, subIndex, isEnabled)}
  onClick={() => handleSubChapterClick(chapter, subchapter, chapterIndex, subIndex, isEnabled)}
  style={isSubChapterSelected(chapterIndex, subIndex) ? {
    backdropFilter: 'blur(8px)',
    boxShadow: '0 2px 8px rgba(59, 130, 246, 0.2)'
  } : undefined}
>
  {/* Existing sub-chapter content */}
</li>
```

### State Persistence Strategy

Selection state will be maintained during:
- Accordion expand/collapse actions
- Component re-renders due to generation progress updates
- Navigation between different chapters

Selection will be cleared when:
- User manually deselects by clicking the same item
- New book outline is generated
- Component unmounts

## Routing & Navigation

No routing changes are required as this is a local UI interaction feature. The selection state is purely for visual feedback and user experience enhancement.

## API Integration Layer

No API changes are required. The selection functionality is purely client-side and enhances the existing content generation workflow without modifying backend interactions.

## Testing Strategy

### Unit Testing Approach

```typescript
describe('SubChapter Selection', () => {
  test('should highlight selected sub-chapter', () => {
    // Test selection visual feedback
  });

  test('should clear previous selection when new item selected', () => {
    // Test single-selection behavior
  });

  test('should preserve existing disabled state logic', () => {
    // Test sequential generation constraints
  });

  test('should toggle selection on same item click', () => {
    // Test deselection functionality
  });

  test('should maintain selection during accordion interactions', () => {
    // Test state persistence
  });
});
```

### Integration Testing

- Verify selection works with existing chapter generation flow
- Test compatibility with accordion expand/collapse behavior
- Validate visual feedback consistency across different browser environments
- Ensure glass-morphism effects render correctly on various screen sizes

### Visual Regression Testing

- Compare selected vs unselected states
- Validate hover effects and transitions
- Test accessibility contrast ratios for selected states
- Verify glass-morphism effects maintain design consistency

## Implementation Considerations

### Performance Optimization

- **CSS-in-JS Avoidance**: Use CSS classes instead of inline styles where possible
- **Memoization**: Consider `useCallback` for click handlers if performance issues arise
- **Re-render Minimization**: State updates are isolated to selection-specific changes

### Accessibility Enhancement

- **Keyboard Navigation**: Future enhancement to support arrow key navigation
- **Screen Reader Support**: ARIA attributes for selection state
- **Focus Management**: Proper focus indicators for selected items
- **High Contrast**: Ensure selection is visible in high contrast modes

### Browser Compatibility

- **Backdrop Filter Support**: Graceful degradation for older browsers
- **CSS Grid/Flexbox**: Already supported in existing implementation
- **Transition Support**: Progressive enhancement for smooth animations

### Responsive Design

- **Mobile Touch Targets**: Existing 2rem padding provides adequate touch targets
- **Tablet Navigation**: Selection feedback remains visible on medium screen sizes
- **Desktop Precision**: Hover effects complement selection states

### Integration with Existing Features

- **Generation Progress**: Selection state independent of content generation status
- **Sequential Generation**: Preserves existing constraints while adding visual feedback
- **Chapter Navigation**: Selection enhances but doesn't interfere with existing navigation patterns
- **Content Display**: Selected state provides clear indication of active content context
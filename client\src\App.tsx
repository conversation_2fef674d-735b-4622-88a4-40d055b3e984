import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import HomePage from "@/pages/HomePage";
import AppPage from "@/pages/AppPage";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { AuthProvider } from "@/context/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import LoginPage from "@/pages/auth/LoginPage";
import SignupPage from "@/pages/auth/SignupPage";
import ForgotPasswordPage from "@/pages/auth/ForgotPasswordPage";
import VerificationPendingPage from "@/pages/auth/VerificationPendingPage";
import CurrentProjectPage from "@/pages/CurrentProjectPage";
import SavedIdeasPage from "@/pages/SavedIdeasPage";
import VerificationBanner from "@/components/auth/VerificationBanner";

function Router() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-1">
        <Switch>
          <Route path="/" component={HomePage} />
          <Route path="/login" component={LoginPage} />
          <Route path="/signup" component={SignupPage} />
          <Route path="/forgot-password" component={ForgotPasswordPage} />
          <Route path="/verify-pending" component={VerificationPendingPage} />
          <Route
            path="/create-ebook"
            component={() => (
              <ProtectedRoute>
                <AppPage />
              </ProtectedRoute>
            )}
          />
          <Route
            path="/current-project"
            component={() => (
              <ProtectedRoute>
                <CurrentProjectPage />
              </ProtectedRoute>
            )}
          />
          <Route
            path="/saved-ideas"
            component={() => (
              <ProtectedRoute>
                <SavedIdeasPage />
              </ProtectedRoute>
            )}
          />
          <Route component={NotFound} />
        </Switch>
      </div>
      <Footer />
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router />
      </AuthProvider>
      <Toaster />
    </QueryClientProvider>
  );
}

export default App;

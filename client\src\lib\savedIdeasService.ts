import { 
  collection, 
  addDoc, 
  getDocs, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  orderBy, 
  Timestamp,
  DocumentData,
  QuerySnapshot
} from 'firebase/firestore';
import { db } from './firebase';
import { SavedIdea, BookTitle, CreateSavedIdeaData } from '@shared/types';

const COLLECTION_NAME = 'saved-ideas';

/**
 * Save a book idea to Firestore
 */
export async function saveIdea(userId: string, bookTitle: BookTitle): Promise<SavedIdea> {
  try {
    const savedIdeaData: CreateSavedIdeaData = {
      userId,
      bookTitle,
      savedAt: new Date()
    };

    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...savedIdeaData,
      savedAt: Timestamp.fromDate(savedIdeaData.savedAt)
    });

    return {
      id: docRef.id,
      userId,
      bookTitle,
      savedAt: savedIdeaData.savedAt
    };
  } catch (error) {
    console.error('Error saving idea:', error);
    throw new Error('Failed to save idea. Please try again.');
  }
}

/**
 * Get all saved ideas for a user
 */
export async function getSavedIdeas(userId: string): Promise<SavedIdea[]> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('userId', '==', userId),
      orderBy('savedAt', 'desc')
    );

    const querySnapshot: QuerySnapshot<DocumentData> = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.userId,
        bookTitle: data.bookTitle,
        savedAt: data.savedAt.toDate()
      };
    });
  } catch (error) {
    console.error('Error getting saved ideas:', error);
    throw new Error('Failed to load saved ideas. Please try again.');
  }
}

/**
 * Delete a saved idea
 */
export async function deleteSavedIdea(ideaId: string): Promise<void> {
  try {
    await deleteDoc(doc(db, COLLECTION_NAME, ideaId));
  } catch (error) {
    console.error('Error deleting idea:', error);
    throw new Error('Failed to delete idea. Please try again.');
  }
}

/**
 * Check if a book title is already saved by user
 */
export async function isIdeaAlreadySaved(userId: string, titleId: string): Promise<boolean> {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('userId', '==', userId),
      where('bookTitle.id', '==', titleId)
    );

    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking if idea exists:', error);
    return false; // Return false on error to allow saving
  }
}

/**
 * Get a specific saved idea by ID
 */
export async function getSavedIdeaById(ideaId: string): Promise<SavedIdea | null> {
  try {
    const docSnapshot = await getDocs(
      query(collection(db, COLLECTION_NAME), where('__name__', '==', ideaId))
    );

    if (docSnapshot.empty) {
      return null;
    }

    const doc = docSnapshot.docs[0];
    const data = doc.data();
    
    return {
      id: doc.id,
      userId: data.userId,
      bookTitle: data.bookTitle,
      savedAt: data.savedAt.toDate()
    };
  } catch (error) {
    console.error('Error getting saved idea:', error);
    return null;
  }
}
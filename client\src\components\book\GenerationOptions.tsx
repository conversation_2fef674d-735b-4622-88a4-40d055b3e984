import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "lucide-react";
import { 
  OutlineGenerationParams, 
  WritingTone, 
  WritingStyle, 
  WritingLanguage,
  TargetAudience
} from "@shared/types";

interface GenerationOptionsProps {
  outlineParams: OutlineGenerationParams;
  onOutlineParamsChange: (params: OutlineGenerationParams) => void;
  contentTone: WritingTone;
  onContentToneChange: (tone: WritingTone) => void;
  contentStyle: WritingStyle;
  onContentStyleChange: (style: WritingStyle) => void;
  contentLanguage: WritingLanguage;
  onContentLanguageChange: (language: WritingLanguage) => void;
  targetAudience: TargetAudience;
  onTargetAudienceChange: (audience: TargetAudience) => void;
}

export default function GenerationOptions({
  outlineParams,
  onOutlineParamsChange,
  contentTone,
  onContentToneChange,
  contentStyle,
  onContentStyleChange,
  contentLanguage,
  onContentLanguageChange,
  targetAudience,
  onTargetAudienceChange
}: GenerationOptionsProps) {
  const [isOpen, setIsOpen] = useState<string[]>([]);



  return (
    <Accordion 
      type="multiple" 
      value={isOpen} 
      onValueChange={setIsOpen}
      className="border rounded-lg divide-y bg-card/30 backdrop-blur-sm border-border/50 shadow-sm"
    >


      <AccordionItem value="content-options" className="border-none">
        <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/10 transition-all duration-200 ease-out">
          <div className="flex items-center text-left">
            <svg className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 19l7-7 3 3-7 7-3-3z" />
              <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z" />
              <path d="M2 2l7.586 7.586" />
              <path d="M11 11l4 4" />
            </svg>
            <span>Writing Style</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-4 pt-1 animate-in slide-in-from-top-2 duration-200">
          <div className="space-y-6 mt-4">
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="tone" className="text-sm mr-2">
                  Tone
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">The overall tone of the writing</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="tone"
                type="text"
                value={contentTone}
                onChange={(e) => onContentToneChange(e.target.value as WritingTone)}
                placeholder="Enter tone (e.g., professional, casual, academic)"
                className="w-full"
              />
            </div>
            
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="style" className="text-sm mr-2">
                  Style
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">The writing style approach</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="style"
                type="text"
                value={contentStyle}
                onChange={(e) => onContentStyleChange(e.target.value as WritingStyle)}
                placeholder="Enter style (e.g., descriptive, analytical, narrative)"
                className="w-full"
              />
            </div>
            
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="language" className="text-sm mr-2">
                  Language Level
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">The complexity of language used</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="language"
                type="text"
                value={contentLanguage}
                onChange={(e) => onContentLanguageChange(e.target.value as WritingLanguage)}
                placeholder="Enter language level (e.g., simple, intermediate, advanced)"
                className="w-full"
              />
            </div>
            
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="target-audience" className="text-sm mr-2">
                  Target Audience
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[200px] text-sm">
                        Specify who your book is written for to tailor content appropriately
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="target-audience"
                value={targetAudience}
                onChange={(e) => onTargetAudienceChange(e.target.value)}
                placeholder="e.g., Business professionals, Students, Beginners"
                maxLength={200}
                className="w-full"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {targetAudience.length}/200 characters
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
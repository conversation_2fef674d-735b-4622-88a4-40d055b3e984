# Language Dropdown Component Design

## Overview

This design document outlines the implementation of a language selection dropdown component that allows users to specify the target language for their book content generation. The component will be integrated into the existing book generation workflow, positioned strategically between the title generation and customization options sections.

## Technology Stack & Dependencies

- **UI Framework**: React with TypeScript
- **Component Library**: Radix UI Select component (existing)
- **Styling**: Tailwind CSS (existing)
- **State Management**: React useState and props
- **Icons**: Lucide React (existing)

## Component Architecture

### Language Selection Component

The language dropdown will be implemented as a standalone component that integrates with the existing OutlineGenerator workflow:

```
LanguageSelector
├── Label (Book Language)
├── Select Trigger (current selection display)
├── Select Content (dropdown options)
│   ├── SelectItem (English - default)
│   ├── SelectItem (Arabic)
│   ├── SelectItem (Bengali)
│   └── ... (other languages)
```

### Component Hierarchy

```
OutlineGenerator
├── Topic Input Section
├── Action Buttons (Generate Titles/Outline)
├── Language Selection Component (NEW)
├── Customization Options Toggle
└── Outline Display
```

### Props/State Management

#### New Type Definitions
```typescript
export type BookLanguage = 
  | 'ar' | 'bn' | 'bg' | 'zh' | 'hr' | 'cs' | 'da' | 'nl' 
  | 'en' | 'et' | 'fi' | 'fr' | 'de' | 'el' | 'iw' | 'hi' 
  | 'hu' | 'id' | 'it' | 'ja' | 'ko' | 'lv' | 'lt' | 'no' 
  | 'pl' | 'pt' | 'ro' | 'ru' | 'sr' | 'sk' | 'sl' | 'es' 
  | 'sw' | 'sv' | 'th' | 'tr' | 'uk' | 'vi';

interface LanguageOption {
  code: BookLanguage;
  name: string;
}
```

#### Component Props
```typescript
interface LanguageSelectorProps {
  selectedLanguage: BookLanguage;
  onLanguageChange: (language: BookLanguage) => void;
  disabled?: boolean;
}
```

### Language Options Data Structure

The component will maintain a comprehensive list of supported languages:

| Language | Code | Name |
|----------|------|------|
| Arabic | ar | Arabic |
| Bengali | bn | Bengali |
| Bulgarian | bg | Bulgarian |
| Chinese | zh | Chinese (Simplified & Traditional) |
| Croatian | hr | Croatian |
| Czech | cs | Czech |
| Danish | da | Danish |
| Dutch | nl | Dutch |
| English | en | English (Default) |
| Estonian | et | Estonian |
| Finnish | fi | Finnish |
| French | fr | French |
| German | de | German |
| Greek | el | Greek |
| Hebrew | iw | Hebrew |
| Hindi | hi | Hindi |
| Hungarian | hu | Hungarian |
| Indonesian | id | Indonesian |
| Italian | it | Italian |
| Japanese | ja | Japanese |
| Korean | ko | Korean |
| Latvian | lv | Latvian |
| Lithuanian | lt | Lithuanian |
| Norwegian | no | Norwegian |
| Polish | pl | Polish |
| Portuguese | pt | Portuguese |
| Romanian | ro | Romanian |
| Russian | ru | Russian |
| Serbian | sr | Serbian |
| Slovak | sk | Slovak |
| Slovenian | sl | Slovenian |
| Spanish | es | Spanish |
| Swahili | sw | Swahili |
| Swedish | sv | Swedish |
| Thai | th | Thai |
| Turkish | tr | Turkish |
| Ukrainian | uk | Ukrainian |
| Vietnamese | vi | Vietnamese |

## Component Positioning & Layout

### Visual Flow Diagram

```mermaid
graph TD
    A[Book Topic Input] --> B[Generate Book Titles Button]
    B --> C[Language Selection Dropdown]
    C --> D[Customization Options Toggle]
    D --> E[Book Outline Display]
    
    style C fill:#e1f5fe,stroke:#01579b,stroke-width:2px
```

### Integration Points

1. **Position**: Between "Generate Book Titles" button and "Customization Options" toggle
2. **Visibility**: Always visible when in title generation or outline generation workflow steps
3. **State Persistence**: Selection persists across workflow steps
4. **Styling Consistency**: Matches existing UI component patterns

## State Management Integration

### Parent Component Updates

The OutlineGenerator component will be enhanced to include:

```typescript
// New state for language selection
const [selectedLanguage, setSelectedLanguage] = useState<BookLanguage>('en');

// Props to be passed down
interface OutlineGeneratorProps {
  // ... existing props
  selectedLanguage: BookLanguage;
  onLanguageChange: (language: BookLanguage) => void;
}
```

### AppPage State Management

The main application state will include:

```typescript
// In AppPage.tsx
const [selectedLanguage, setSelectedLanguage] = useState<BookLanguage>('en');

// Handler function
const handleLanguageChange = (language: BookLanguage) => {
  setSelectedLanguage(language);
};
```

## API Integration

### Backend Integration Points

The selected language will be passed to content generation APIs:

1. **Title Generation**: `/api/generate-titles`
   - Add `targetLanguage` parameter
   - Modify prompt to include language specification

2. **Outline Generation**: `/api/generate-outline`
   - Include language in generation parameters
   - Update AI prompts for target language

3. **Content Generation**: `/api/generate-content`
   - Pass language preference for chapter content
   - Ensure consistent language usage

### Updated API Parameters

```typescript
interface OutlineGenerationParams {
  maxChapters: number;
  maxSubChapters: number;
  tone?: WritingTone;
  style?: WritingStyle;
  language?: WritingLanguage;
  targetLanguage?: BookLanguage; // NEW
}
```

## Design System Consistency

### Styling Architecture

The component will follow the existing design patterns:

- **Container**: Same border and spacing as other form elements
- **Label**: Consistent typography with existing labels
- **Select Component**: Uses the existing Radix UI Select implementation
- **Focus States**: Matches existing form component focus indicators
- **Hover States**: Consistent with other interactive elements

### Visual Design Specifications

```css
/* Component container */
.language-selector {
  margin-bottom: 1rem; /* 16px */
  width: 100%;
}

/* Label styling */
.language-label {
  font-size: 0.875rem; /* 14px */
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  margin-bottom: 0.25rem; /* 4px */
}

/* Select trigger */
.language-select-trigger {
  height: 2.5rem; /* 40px */
  width: 100%;
  border-radius: 0.375rem; /* 6px */
  border: 1px solid hsl(var(--border));
  background: hsl(var(--background));
}
```

## User Experience Flow

### Interaction Patterns

1. **Default State**: English is pre-selected
2. **Selection**: Clicking opens dropdown with alphabetically sorted options
3. **Search**: Users can type to filter language options
4. **Confirmation**: Selection immediately updates and closes dropdown
5. **Persistence**: Choice remains throughout the session

### Accessibility Features

- **Keyboard Navigation**: Full keyboard support for dropdown interaction
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Clear focus indicators and logical tab order
- **Language Names**: Display full language names (not just codes)

### Error Handling

- **Fallback**: Defaults to English if invalid language code provided
- **Validation**: Ensures selected language is supported
- **Network Issues**: Graceful degradation if API calls fail

## Testing Strategy

### Unit Testing Requirements

1. **Component Rendering**: Verify dropdown renders with all language options
2. **Selection Logic**: Test language selection and state updates
3. **Default Behavior**: Confirm English is default selection
4. **Props Handling**: Validate prop changes trigger re-renders
5. **Integration**: Test integration with parent components

### Test Scenarios

```typescript
describe('LanguageSelector', () => {
  it('renders with English as default selection');
  it('displays all 36 language options');
  it('calls onLanguageChange when selection changes');
  it('shows correct language name for each code');
  it('handles disabled state correctly');
  it('maintains accessibility standards');
});
```

## Implementation Phases

### Phase 1: Core Component Development
- Create LanguageSelector component
- Implement dropdown with all language options
- Add basic styling and functionality

### Phase 2: Integration
- Integrate with OutlineGenerator
- Update parent component state management
- Position component in correct location

### Phase 3: API Integration
- Update backend endpoints to accept language parameter
- Modify AI prompts for multi-language support
- Test content generation in different languages

### Phase 4: Enhancement & Testing
- Add search/filter functionality
- Implement comprehensive testing
- Performance optimization and error handling
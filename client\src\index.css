@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Glass Theme Color Variables */
    --background: 220 20% 6%;
    --foreground: 220 15% 85%;
    --card: 220 15% 15%;
    --card-foreground: 220 15% 85%;
    --popover: 220 15% 12%;
    --popover-foreground: 220 15% 85%;
    --primary: 220 15% 85%;
    --primary-foreground: 220 20% 10%;
    --secondary: 220 15% 25%;
    --secondary-foreground: 220 15% 85%;
    --muted: 220 15% 35%;
    --muted-foreground: 220 15% 65%;
    --accent: 220 40% 65%;
    --accent-foreground: 220 15% 85%;
    --destructive: 0 62% 50%;
    --destructive-foreground: 220 15% 85%;
    --border: 220 15% 30%;
    --input: 220 15% 25%;
    --ring: 220 40% 65%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 220 15% 12%;
    --sidebar-foreground: 220 15% 85%;
    --sidebar-primary: 220 15% 85%;
    --sidebar-primary-foreground: 220 20% 10%;
    --sidebar-accent: 220 40% 65%;
    --sidebar-accent-foreground: 220 15% 85%;
    --sidebar-border: 220 15% 30%;
    --sidebar-ring: 220 40% 65%;
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background: linear-gradient(135deg, hsl(220, 20%, 6%) 0%, hsl(220, 25%, 8%) 100%);
    min-height: 100vh;
  }

  /* Glass effect base styles */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.3);
  }

  .glass-nav {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  }

  /* Gradient Button Styles */
  .gradient-button {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.9), rgba(79, 70, 229, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button:hover {
    background: linear-gradient(to right, rgba(37, 99, 235, 1), rgba(67, 56, 202, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.5);
  }

  .gradient-button-secondary {
    background: linear-gradient(to right, rgba(168, 85, 247, 0.9), rgba(139, 92, 246, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(168, 85, 247, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button-secondary:hover {
    background: linear-gradient(to right, rgba(147, 51, 234, 1), rgba(126, 34, 206, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(168, 85, 247, 0.5);
  }

  .gradient-button-destructive {
    background: linear-gradient(to right, rgba(239, 68, 68, 0.9), rgba(225, 29, 72, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button-destructive:hover {
    background: linear-gradient(to right, rgba(220, 38, 38, 1), rgba(190, 18, 60, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(239, 68, 68, 0.5);
  }

  /* Legacy glass button styles - keeping for backward compatibility */
  .glass-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(31, 38, 135, 0.4);
  }

  .glass-input {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
  }

  .glass-input:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }

  /* Premium Loading Animations */
  
  /* Hardware acceleration for all animated elements */
  .animate-glass-spin,
  .animate-shimmer,
  .animate-typewriter,
  .animate-pulse-glow,
  .animate-particle-float,
  .animate-ripple,
  .animate-scale-bounce {
    will-change: transform, opacity;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
  }

  /* Shimmer effect with better performance */
  .animate-shimmer {
    background-size: 200px 100%;
    background-repeat: no-repeat;
    animation: shimmer 2s infinite ease-in-out;
  }

  /* Glass spinner glow effect */
  .glass-spinner-glow {
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.4), transparent);
    animation: glass-spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    z-index: -1;
  }

  /* Typewriter cursor */
  .typewriter-cursor {
    display: inline-block;
    width: 2px;
    height: 1em;
    background-color: currentColor;
    animation: blink 1s step-end infinite;
  }

  /* Loading text dots animation */
  .loading-dots {
    display: inline-flex;
    gap: 1px;
  }

  .loading-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: loading-dots 1.4s infinite ease-in-out;
  }

  .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
  .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
  .loading-dots span:nth-child(3) { animation-delay: 0; }

  @keyframes loading-dots {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Particle floating animation */
  .particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.6);
    pointer-events: none;
    z-index: 1;
  }

  /* Content writing animation */
  .writing-animation {
    position: relative;
    overflow: hidden;
  }

  .writing-animation::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(59, 130, 246, 0.8), transparent);
    animation: writing-cursor 1.5s ease-in-out infinite;
  }

  @keyframes writing-cursor {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-glass-spin,
    .animate-shimmer,
    .animate-typewriter,
    .animate-pulse-glow,
    .animate-particle-float,
    .animate-ripple,
    .animate-scale-bounce,
    .loading-dots span,
    .writing-animation::after {
      animation: none !important;
    }

    .glass-spinner-glow {
      display: none;
    }

    /* Provide static alternatives */
    .animate-shimmer {
      opacity: 0.7;
    }

    .typewriter-cursor {
      opacity: 0.5;
    }

    .loading-dots span {
      opacity: 0.7;
      transform: scale(0.8);
    }
  }

  /* Focus styles for accessibility */
  .loading-button:focus-visible {
    outline: 2px solid rgba(59, 130, 246, 0.8);
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .glass,
    .glass-card {
      border-width: 2px;
      border-color: currentColor;
    }

    .animate-shimmer {
      background: linear-gradient(90deg, transparent 0px, currentColor 40px, transparent 80px);
      opacity: 0.3;
    }
  }
}
import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { getSavedIdeas, deleteSavedIdea } from '@/lib/savedIdeasService';
import { SavedIdea } from '@shared/types';
import { BookmarkIcon, TrashIcon, PlayIcon, CalendarIcon } from 'lucide-react';

export default function SavedIdeasPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Fetch saved ideas
  const { data: savedIdeas, isLoading, error } = useQuery({
    queryKey: ['savedIdeas', user?.uid],
    queryFn: () => {
      if (!user?.uid) throw new Error('User not authenticated');
      return getSavedIdeas(user.uid);
    },
    enabled: !!user?.uid,
  });

  const handleDeleteIdea = async (ideaId: string) => {
    setDeletingId(ideaId);
    try {
      await deleteSavedIdea(ideaId);
      queryClient.invalidateQueries({ queryKey: ['savedIdeas', user?.uid] });
      toast({
        title: "Idea Deleted",
        description: "The saved idea has been removed from your collection.",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete idea. Please try again.",
        variant: "destructive"
      });
    } finally {
      setDeletingId(null);
    }
  };

  const handleContinueWithIdea = (savedIdea: SavedIdea) => {
    // Store the selected idea in localStorage for the AppPage to pick up
    localStorage.setItem('selectedBookTitle', JSON.stringify(savedIdea.bookTitle));
    
    // Navigate to create-ebook page
    setLocation('/create-ebook');
    
    toast({
      title: "Idea Loaded",
      description: "Your saved idea has been loaded into the eBook creator.",
      variant: "default"
    });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <Skeleton className="h-10 w-64 mb-2" />
            <Skeleton className="h-6 w-96" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="h-80">
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3 mb-4" />
                  <Skeleton className="h-10 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-destructive mb-2">Failed to Load Saved Ideas</h2>
            <p className="text-muted-foreground">
              {error instanceof Error ? error.message : 'An unexpected error occurred. Please try again.'}
            </p>
            <Button 
              onClick={() => queryClient.invalidateQueries({ queryKey: ['savedIdeas', user?.uid] })}
              className="mt-4"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <BookmarkIcon className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold">Saved Ideas</h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Your collection of saved book ideas. Continue working on any idea or explore new possibilities.
          </p>
        </div>

        {/* Empty State */}
        {!savedIdeas || savedIdeas.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-muted/30 border-2 border-dashed border-muted-foreground/30 rounded-lg p-8 max-w-md mx-auto">
              <BookmarkIcon className="h-16 w-16 text-muted-foreground/50 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No Saved Ideas Yet</h3>
              <p className="text-muted-foreground mb-4">
                Start by generating book titles and saving the ones you like for later.
              </p>
              <Button 
                onClick={() => setLocation('/create-ebook')}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
              >
                <PlayIcon className="w-4 h-4 mr-2" />
                Create eBook
              </Button>
            </div>
          </div>
        ) : (
          /* Ideas Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {savedIdeas.map((savedIdea) => (
              <Card key={savedIdea.id} className="h-full flex flex-col hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg line-clamp-2 leading-tight">
                    {savedIdea.bookTitle.title}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-1 text-sm">
                    <CalendarIcon className="h-3 w-3" />
                    Saved {formatDate(savedIdea.savedAt)}
                  </CardDescription>
                </CardHeader>

                <CardContent className="flex-1 flex flex-col">
                  {/* Book Summary */}
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-3 flex-1">
                    {savedIdea.bookTitle.summary}
                  </p>

                  {/* Book Details */}
                  <div className="mb-4 space-y-2">
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="secondary" className="text-xs">
                        {savedIdea.bookTitle.chapterCount} chapters
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {savedIdea.bookTitle.subChapterCounts.reduce((a, b) => a + b, 0)} sub-chapters
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="font-medium text-muted-foreground">Tone:</span>
                        <p className="capitalize">{savedIdea.bookTitle.tone}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Style:</span>
                        <p className="capitalize">{savedIdea.bookTitle.style}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Level:</span>
                        <p className="capitalize">{savedIdea.bookTitle.languageLevel}</p>
                      </div>
                      <div>
                        <span className="font-medium text-muted-foreground">Audience:</span>
                        <p className="line-clamp-1">{savedIdea.bookTitle.targetAudience}</p>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2 mt-auto">
                    <Button
                      onClick={() => handleContinueWithIdea(savedIdea)}
                      className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                      size="sm"
                    >
                      <PlayIcon className="w-4 h-4 mr-2" />
                      Continue with this idea
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-destructive/30 text-destructive hover:bg-destructive/10"
                          disabled={deletingId === savedIdea.id}
                        >
                          {deletingId === savedIdea.id ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-destructive mr-2"></div>
                              Deleting...
                            </>
                          ) : (
                            <>
                              <TrashIcon className="w-4 h-4 mr-2" />
                              Delete
                            </>
                          )}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Saved Idea?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete "{savedIdea.bookTitle.title}"? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteIdea(savedIdea.id)}
                            className="bg-destructive hover:bg-destructive/90"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Footer CTA */}
        {savedIdeas && savedIdeas.length > 0 && (
          <div className="mt-12 text-center">
            <div className="bg-gradient-to-r from-primary/10 to-blue-500/10 border border-primary/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-2">Ready to create something new?</h3>
              <p className="text-muted-foreground mb-4">
                Generate new book ideas and expand your collection.
              </p>
              <Button 
                onClick={() => setLocation('/create-ebook')}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
              >
                <PlayIcon className="w-4 h-4 mr-2" />
                Create New eBook
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
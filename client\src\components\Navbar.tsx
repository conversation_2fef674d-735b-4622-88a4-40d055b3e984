import { Link } from "wouter";
import { useAuth } from "@/context/AuthContext";
import UserMenu from "@/components/UserMenu";

export default function Navbar() {
  const { user } = useAuth();
  return (
    <nav className="glass-nav shadow-lg relative z-[100000]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <svg className="h-8 w-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7v10l10 5 10-5V7L12 2zm0 2.5L20 9v7l-8 4-8-4V9l8-4.5z" />
              </svg>
              <span className="ml-2 text-xl font-semibold">AIeBookWriter.Pro</span>
            </div>
          </div>
          <div className="flex items-center">
            <Link href="/">
              <a className="px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Home</a>
            </Link>
            <Link href="/create-ebook">
              <a className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Create eBook</a>
            </Link>
            {!user ? (
              <>
                <Link href="/login">
                  <a className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Login</a>
                </Link>
                <Link href="/signup">
                  <a className="ml-2 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Sign Up</a>
                </Link>
              </>
            ) : (
              <UserMenu />
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

# Target Audience Field Implementation Design

## Overview

This design document outlines the implementation of a Target Audience text input field within the Writing Style section of the NonFictionBookBuilder application. The feature enables users to specify their book's intended readership, allowing for more targeted and contextually appropriate content generation across all book generation workflows.

## Architecture

The Target Audience field integration follows the existing pattern-based architecture, extending the current Writing Style customization system with audience-aware content generation capabilities.

```mermaid
graph TD
    A[User Input: Target Audience] --> B[GenerationOptions Component]
    B --> C[Writing Style Section]
    C --> D[Target Audience Field]
    D --> E[State Management]
    E --> F[Content Generation Pipeline]
    F --> G[AI Prompt Enhancement]
    G --> H[Audience-Aware Content]
    
    I[AppPage State] --> E
    E --> J[OutlineGenerator]
    E --> K[ContentGenerator]
    J --> L[Outline API]
    K --> M[Chapter API]
    L --> N[Gemini AI]
    M --> N
```

## Data Models & Schema Updates

### Type System Extensions

Add target audience type and extend existing interfaces:

```typescript
// New type for target audience
export type TargetAudience = string;

// Extended OutlineGenerationParams interface
export interface OutlineGenerationParams {
  maxChapters: number;
  maxSubChapters: number;
  tone?: WritingTone;
  style?: WritingStyle;
  language?: WritingLanguage;
  targetLanguage?: BookLanguage;
  targetAudience?: TargetAudience; // New field
}

// Extended ContentGenerationParams interface
export interface ContentGenerationParams {
  tone: WritingTone;
  style: WritingStyle;
  language: WritingLanguage;
  targetLanguage?: BookLanguage;
  targetAudience?: TargetAudience; // New field
}
```

### Component Interface Updates

Update GenerationOptions component props:

```typescript
interface GenerationOptionsProps {
  outlineParams: OutlineGenerationParams;
  onOutlineParamsChange: (params: OutlineGenerationParams) => void;
  contentTone: WritingTone;
  onContentToneChange: (tone: WritingTone) => void;
  contentStyle: WritingStyle;
  onContentStyleChange: (style: WritingStyle) => void;
  contentLanguage: WritingLanguage;
  onContentLanguageChange: (language: WritingLanguage) => void;
  targetAudience: TargetAudience; // New prop
  onTargetAudienceChange: (audience: TargetAudience) => void; // New prop
}
```

## Component Architecture

### Writing Style Section Enhancement

Extend the existing Writing Style accordion section with the Target Audience field:

```mermaid
classDiagram
    class GenerationOptions {
        +targetAudience: TargetAudience
        +onTargetAudienceChange: (audience) => void
        +renderTargetAudienceField() : JSX.Element
        +validateTargetAudience(value) : boolean
    }
    
    class TargetAudienceField {
        +value: string
        +onChange: (value) => void
        +placeholder: string
        +defaultValue: string
        +validation: string
    }
    
    class WritingStyleSection {
        +tone: WritingTone
        +style: WritingStyle
        +language: WritingLanguage
        +targetAudience: TargetAudience
    }
    
    GenerationOptions --> TargetAudienceField
    GenerationOptions --> WritingStyleSection
```

### UI Component Specification

**Target Audience Field Layout:**
- **Position**: After Language Level field in Writing Style section
- **Input Type**: Text input field with validation
- **Default Value**: "General Audience" 
- **Placeholder**: "e.g., Business professionals, Students, Beginners"
- **Label**: "Target Audience" with info tooltip
- **Tooltip Content**: "Specify who your book is written for to tailor content appropriately"

**Visual Design Pattern:**
```mermaid
graph LR
    A[Tone Selector] --> B[Style Selector]
    B --> C[Language Level Selector]
    C --> D[Target Audience Input]
    D --> E[Validation & State Update]
```

## API Integration Layer

### Prompt Enhancement Strategy

The Target Audience field integrates into existing AI generation prompts through enhanced context injection:

**Outline Generation Integration:**
```typescript
// Enhanced prompt construction
let audienceGuidance = "";
if (params.targetAudience && params.targetAudience !== "General Audience") {
  audienceGuidance = `
  Target Audience Guidance:
  - Tailor content complexity and examples for: ${params.targetAudience}
  - Use language and references appropriate for this audience
  - Structure chapters to match audience knowledge level and interests
  `;
}

const prompt = `
You are an expert eBook outliner for non-fiction books. Create a detailed, hierarchical outline for a book on the following topic: "${userInput}".

The outline should have up to ${params.maxChapters} main chapters with up to ${params.maxSubChapters} subchapters each.

IMPORTANT: Generate all content in ${targetLanguage}. All chapter titles and subchapter titles must be written in ${targetLanguage}.

${styleGuidance}
${audienceGuidance}

Format your response strictly as a JSON object...
`;
```

**Chapter Content Generation Integration:**
```typescript
// Enhanced content generation with audience awareness
const audienceContext = params.targetAudience && params.targetAudience !== "General Audience" 
  ? `\n\nTarget Audience: ${params.targetAudience}\n- Adapt examples, complexity, and tone for this specific audience\n- Use terminology and references relevant to their background and interests`
  : "";

const prompt = `
You are an expert non-fiction book writer for Amazon KDP. Write detailed, informative content for the following subchapter:

Book Topic: "${bookTopic}"
Main Chapter: "${mainChapterTitle}"
Subchapter: "${subChapterTitle}"

IMPORTANT: Write all content in ${targetLanguage}. The entire chapter content must be written in ${targetLanguage}.

Writing Style:
- Tone: ${toneDescription}
- Style: ${styleDescription}
- Language Level: ${languageDescription}${audienceContext}

Guidelines:
- Write approximately 800-1200 words of polished, publication-ready content
- Include relevant headings where appropriate (using markdown format: # ## ###)
- Include practical examples, actionable advice, and evidence-based information
- Format the text with clear paragraphs, occasional bullet points for key points, and proper structure
- Ensure this subchapter flows logically while being able to stand alone
- Tailor examples and complexity level specifically for the target audience
...
`;
```

## State Management Integration

### AppPage State Extensions

Extend the main application state to include target audience:

```typescript
// AppPage component state additions
const [targetAudience, setTargetAudience] = useState<TargetAudience>("General Audience");

// Updated outline parameters state
const [outlineParams, setOutlineParams] = useState<OutlineGenerationParams>({
  maxChapters: 5,
  maxSubChapters: 3,
  targetAudience: "General Audience" // Default value
});

// Enhanced parameter update handler
const handleTargetAudienceChange = (audience: TargetAudience) => {
  setTargetAudience(audience);
  setOutlineParams(prev => ({
    ...prev,
    targetAudience: audience
  }));
};
```

### Prop Threading Pattern

Update component prop passing to include target audience:

```mermaid
sequenceDiagram
    participant User
    participant AppPage
    participant OutlineGenerator
    participant GenerationOptions
    participant API
    
    User->>GenerationOptions: Enters target audience
    GenerationOptions->>AppPage: onTargetAudienceChange("Business professionals")
    AppPage->>AppPage: Updates state
    AppPage->>OutlineGenerator: Passes updated props
    User->>OutlineGenerator: Generates outline
    OutlineGenerator->>API: Sends request with audience context
    API->>API: Enhances prompts with audience guidance
    API-->>OutlineGenerator: Returns audience-tailored outline
```

## Validation & Error Handling

### Input Validation Rules

**Target Audience Field Validation:**
- **Maximum Length**: 200 characters
- **Minimum Length**: 2 characters (when not default)
- **Character Set**: Alphanumeric, spaces, common punctuation
- **Prohibited**: HTML tags, script content, excessive special characters
- **Empty Handling**: Revert to "General Audience" if empty

**Validation Implementation:**
```typescript
const validateTargetAudience = (value: string): { isValid: boolean; error?: string } => {
  if (!value.trim()) {
    return { isValid: true }; // Empty reverts to default
  }
  
  if (value.length < 2) {
    return { isValid: false, error: "Target audience must be at least 2 characters" };
  }
  
  if (value.length > 200) {
    return { isValid: false, error: "Target audience must be less than 200 characters" };
  }
  
  // Check for HTML tags or script content
  if (/<[^>]*>/.test(value) || /script/i.test(value)) {
    return { isValid: false, error: "Invalid characters detected" };
  }
  
  return { isValid: true };
};
```

## Data Persistence Strategy

### LocalStorage Integration

Target audience preferences persist with other customization settings:

```typescript
// Enhanced localStorage keys
const STORAGE_KEYS = {
  TARGET_AUDIENCE: 'nfb-target-audience',
  OUTLINE_PARAMS: 'nfb-outline-params',
  CONTENT_SETTINGS: 'nfb-content-settings'
};

// Persistence helper functions
const saveTargetAudience = (audience: TargetAudience) => {
  localStorage.setItem(STORAGE_KEYS.TARGET_AUDIENCE, audience);
};

const loadTargetAudience = (): TargetAudience => {
  return localStorage.getItem(STORAGE_KEYS.TARGET_AUDIENCE) || "General Audience";
};

// Enhanced outline params persistence
const saveOutlineParams = (params: OutlineGenerationParams) => {
  localStorage.setItem(STORAGE_KEYS.OUTLINE_PARAMS, JSON.stringify(params));
};
```

### Content Generation Context

Target audience context integrates with existing content caching:

```typescript
// Enhanced content storage key generation
const generateContentStorageKey = (chapter: string, subchapter: string, audience?: string) => {
  const audienceKey = audience && audience !== "General Audience" 
    ? `-audience-${audience.replace(/\s+/g, '-').toLowerCase()}` 
    : "";
  return `chapter-content-${chapter}-${subchapter}${audienceKey}`;
};
```

## User Experience Enhancements

### Field Interaction Design

**Target Audience Input Field Features:**
- **Auto-save**: Input changes save automatically with debouncing (500ms delay)
- **Placeholder Examples**: "e.g., Business professionals, Students, Beginners"
- **Character Counter**: Displays remaining characters (200 max)
- **Clear Button**: Easy reset to default value
- **Validation Feedback**: Real-time validation with clear error messages

### Accessibility Considerations

**ARIA Implementation:**
```typescript
<div className="space-y-2">
  <div className="flex items-center mb-1">
    <Label htmlFor="target-audience" className="text-sm mr-2">
      Target Audience
    </Label>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <InfoIcon className="h-4 w-4 text-muted-foreground" />
        </TooltipTrigger>
        <TooltipContent>
          <p className="w-[200px] text-sm">
            Specify who your book is written for to tailor content appropriately
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
  <Input
    id="target-audience"
    value={targetAudience}
    onChange={(e) => handleTargetAudienceChange(e.target.value)}
    placeholder="e.g., Business professionals, Students, Beginners"
    maxLength={200}
    aria-describedby="target-audience-help"
    className="w-full"
  />
  <div 
    id="target-audience-help" 
    className="text-xs text-muted-foreground"
  >
    {targetAudience.length}/200 characters
  </div>
</div>
```

## Integration Testing Strategy

### Component Testing Requirements

**GenerationOptions Component Tests:**
- Target audience field rendering
- Default value initialization ("General Audience")
- Input change handling and validation
- Props passing to parent components
- Accessibility compliance

**API Integration Tests:**
- Outline generation with audience context
- Chapter generation with audience-specific prompts
- Parameter validation in API calls
- Error handling for invalid audience values

**State Management Tests:**
- Target audience state persistence
- Integration with existing outline parameters
- Prop threading through component hierarchy
- LocalStorage read/write operations

### End-to-End Testing Scenarios

**User Workflow Tests:**
1. **Default Behavior**: Verify "General Audience" appears by default
2. **Custom Audience Input**: Enter custom audience and verify it propagates through generation
3. **Content Variation**: Generate content with different audiences and verify contextual differences
4. **Persistence**: Reload page and verify audience setting persists
5. **Validation**: Test input limits and error states

## Performance Considerations

### Optimization Strategies

**Debounced Input Handling:**
```typescript
import { useMemo } from 'react';
import { debounce } from 'lodash';

// Debounced handler for target audience changes
const debouncedAudienceChange = useMemo(
  () => debounce((audience: TargetAudience) => {
    onTargetAudienceChange(audience);
    saveTargetAudience(audience);
  }, 500),
  [onTargetAudienceChange]
);
```

**Content Cache Optimization:**
- Separate cache entries for different audience specifications
- Cache invalidation when audience changes significantly
- Lazy loading of audience-specific content variations

### Memory Management

**State Cleanup:**
- Proper cleanup of debounced functions
- Memory-efficient string handling for audience values
- Optimized re-rendering with React.memo where appropriate

## Security Considerations

### Input Sanitization

**XSS Prevention:**
```typescript
const sanitizeTargetAudience = (input: string): string => {
  return input
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/[<>\"'&]/g, '') // Remove potentially dangerous characters
    .replace(/script/gi, '') // Remove script references
    .trim()
    .substring(0, 200); // Enforce length limit
};
```

**Content Security:**
- Escape audience input in API prompts
- Validate audience context before AI generation
- Monitor for prompt injection attempts through audience field

## Implementation Roadmap

### Phase 1: Core Implementation
1. **Type System Updates** (shared/types.ts)
   - Add TargetAudience type
   - Extend OutlineGenerationParams and ContentGenerationParams interfaces

2. **Component Development** (GenerationOptions.tsx)
   - Add target audience input field to Writing Style section
   - Implement validation and change handlers
   - Update component props and interfaces

3. **State Management** (AppPage.tsx)
   - Add target audience state variables
   - Implement change handlers and prop threading
   - Update component prop passing

### Phase 2: API Integration
1. **Backend Updates** (gemini.ts)
   - Enhance prompt generation with audience context
   - Update outline and content generation functions
   - Add audience-specific guidance in AI prompts

2. **Route Updates** (routes.ts)
   - Update API endpoint parameter handling
   - Ensure target audience propagation through requests

### Phase 3: Persistence & UX
1. **Data Persistence**
   - localStorage integration for audience preferences
   - Content cache updates with audience context
   - Session persistence across page reloads

2. **User Experience Enhancements**
   - Accessibility improvements
   - Validation feedback implementation
   - Character counting and input helpers

### Phase 4: Testing & Optimization
1. **Testing Implementation**
   - Unit tests for component functionality
   - Integration tests for API workflows
   - End-to-end user journey tests

2. **Performance Optimization**
   - Debounced input handling
   - Memory usage optimization
   - Cache strategy refinement
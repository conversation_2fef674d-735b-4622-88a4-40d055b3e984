functionsDirectory = "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\netlify\\functions"
functionsDirectoryOrigin = "config-v1"
redirectsOrigin = "config"
plugins = []
headers = []

[build]
publish = "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\dist\\public"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"
functions = "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\netlify\\functions"

[build.environment]
NODE_VERSION = "18"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[functions]

[functions."*"]

[[redirects]]
from = "/api/*"
to = "/.netlify/functions/:splat"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]
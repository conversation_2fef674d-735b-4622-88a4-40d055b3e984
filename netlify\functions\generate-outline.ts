import { <PERSON><PERSON> } from '@netlify/functions';
import { GoogleGenerativeAI } from "@google/generative-ai";

// Import shared types
import { OutlineGenerationParams, BookLanguage } from "../../shared/types";

// Initialize the API with the API key
const API_KEY = process.env.GEMINI_API_KEY;
if (!API_KEY) {
  throw new Error("GEMINI_API_KEY environment variable is required");
}
const MODEL = "gemini-2.0-flash";

const genAI = new GoogleGenerativeAI(API_KEY);
const model = genAI.getGenerativeModel({ model: MODEL });

// Helper functions for tone/style descriptions
function getToneDescription(tone: string): string {
  const toneDescriptions = {
    professional: "formal, authoritative, and business-like",
    casual: "relaxed, conversational, and friendly",
    academic: "scholarly, rigorous, and evidence-based",
    conversational: "engaging, relatable, and approachable",
    instructional: "clear, directive, and educational"
  };
  return toneDescriptions[tone as keyof typeof toneDescriptions] || toneDescriptions.professional;
}

function getStyleDescription(style: string): string {
  const styleDescriptions = {
    descriptive: "rich in detail and imagery",
    analytical: "focused on breaking down concepts and examining relationships",
    persuasive: "aimed at convincing the reader with compelling arguments",
    narrative: "story-driven with clear flow and progression",
    technical: "precise, detailed, and specialized"
  };
  return styleDescriptions[style as keyof typeof styleDescriptions] || styleDescriptions.descriptive;
}

function getLanguageDescription(language: string): string {
  const languageDescriptions = {
    simple: "using straightforward vocabulary and short sentences accessible to a wide audience",
    intermediate: "balancing clarity with some field-specific terminology",
    advanced: "using sophisticated vocabulary and complex sentence structures",
    technical: "employing specialized terminology and jargon appropriate for experts"
  };
  return languageDescriptions[language as keyof typeof languageDescriptions] || languageDescriptions.intermediate;
}

function getTargetLanguageName(languageCode: BookLanguage): string {
  const languageNames: Record<BookLanguage, string> = {
    'ar': 'Arabic',
    'bn': 'Bengali',
    'bg': 'Bulgarian',
    'zh-CN': 'Chinese (Simplified)',
    'zh-TW': 'Chinese (Traditional)',
    'hr': 'Croatian',
    'cs': 'Czech',
    'da': 'Danish',
    'nl': 'Dutch',
    'en': 'English',
    'et': 'Estonian',
    'fi': 'Finnish',
    'fr': 'French',
    'de': 'German',
    'el': 'Greek',
    'he': 'Hebrew',
    'hi': 'Hindi',
    'hu': 'Hungarian',
    'id': 'Indonesian',
    'it': 'Italian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'no': 'Norwegian',
    'pl': 'Polish',
    'pt': 'Portuguese',
    'ro': 'Romanian',
    'ru': 'Russian',
    'sr': 'Serbian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'es': 'Spanish',
    'sw': 'Swahili',
    'sv': 'Swedish',
    'th': 'Thai',
    'tr': 'Turkish',
    'uk': 'Ukrainian',
    'vi': 'Vietnamese'
  };
  return languageNames[languageCode] || 'English';
}

async function generateOutline(
  userInput: string, 
  params: OutlineGenerationParams
): Promise<{
  chapters: Array<{ title: string; subchapters: string[] }>;
}> {
  try {
    // Build outline generation parameters from user settings
    const toneDescription = params.tone ? getToneDescription(params.tone) : "";
    const styleDescription = params.style ? getStyleDescription(params.style) : "";
    const languageDescription = params.language ? getLanguageDescription(params.language) : "";
    const targetLanguage = params.targetLanguage ? getTargetLanguageName(params.targetLanguage) : "English";

    // Optional writing style guidance section
    let styleGuidance = "";
    if (params.tone || params.style || params.language) {
      styleGuidance = `
      Writing style guidance:
      ${params.tone ? `- Use a ${toneDescription} tone` : ""}
      ${params.style ? `- Write in a ${styleDescription} style` : ""}
      ${params.language ? `- Use ${languageDescription} language level` : ""}
      `;
    }
    
    // Build audience guidance if target audience is specified
    let audienceGuidance = "";
    if (params.targetAudience && params.targetAudience !== "General Audience") {
      audienceGuidance = `
      Target Audience Guidance:
      - Tailor content complexity and examples for: ${params.targetAudience}
      - Use language and references appropriate for this audience
      - Structure chapters to match audience knowledge level and interests
      `;
    }

    // Build chapter structure guidance based on available parameters
    let chapterStructureGuidance = "";
    if (params.subChapterCounts && params.subChapterCounts.length > 0) {
      // Use specific sub-chapter counts for dynamic distribution
      chapterStructureGuidance = `
    The outline should have exactly ${params.maxChapters} main chapters with the following specific sub-chapter distribution:
    ${params.subChapterCounts.map((count, index) => `Chapter ${index + 1}: ${count} sub-chapters`).join('\n    ')}

    IMPORTANT: Follow this exact structure - each chapter must have the specified number of sub-chapters.`;
    } else {
      // Fall back to uniform distribution for backward compatibility
      chapterStructureGuidance = `
    The outline should have up to ${params.maxChapters} main chapters with up to ${params.maxSubChapters} subchapters each.`;
    }

    const prompt = `
    You are an expert eBook outliner for non-fiction books. Create a detailed, hierarchical outline for a book on the following topic: "${userInput}".

    ${chapterStructureGuidance}

    IMPORTANT: Generate all content in ${targetLanguage}. All chapter titles and subchapter titles must be written in ${targetLanguage}.

    ${styleGuidance}${audienceGuidance}

    Format your response strictly as a JSON object with the following structure:
    {
      "chapters": [
        {
          "title": "Chapter Title",
          "subchapters": ["Subchapter 1 Title", "Subchapter 2 Title", "Subchapter 3 Title"]
        }
      ]
    }
    
    Ensure the book follows a logical progression, starting with foundational concepts and moving toward more advanced topics. Each chapter should have a clear purpose, and subchapters should break down the main ideas in a sequential manner.
    
    Only return the JSON structure, no additional text or explanation.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Extract JSON from the response (in case there's extra text)
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("Failed to generate valid JSON outline");
    }

    const parsedOutline = JSON.parse(jsonMatch[0]);
    
    // Validate the structure
    if (!parsedOutline.chapters || !Array.isArray(parsedOutline.chapters)) {
      throw new Error("Invalid outline structure");
    }

    return parsedOutline;
  } catch (error) {
    console.error("Error in generateOutline:", error);
    throw new Error("Failed to generate book outline");
  }
}

export const handler: Handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle OPTIONS request for CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: 'Method Not Allowed' })
    };
  }

  try {
    const { selectedTitle, userInput, generationParams } = JSON.parse(event.body || '{}');
    
    // Use selected title as primary input, fallback to userInput
    const bookTitle = selectedTitle?.title || userInput;
    
    if (!bookTitle || typeof bookTitle !== "string") {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          message: "Invalid input. Please provide a selected title or user input." 
        })
      };
    }

    // Validate generation parameters
    const params: OutlineGenerationParams = generationParams || {
      maxChapters: 5,
      maxSubChapters: 3
    };

    // Add explicit validation for minimum values
    if (params.maxChapters < 5) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          message: "Invalid parameters. Minimum chapters allowed is 5." 
        })
      };
    }

    if (params.maxSubChapters < 3) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          message: "Invalid parameters. Minimum sub-chapters allowed is 3." 
        })
      };
    }

    const outline = await generateOutline(bookTitle, params);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ outline })
    };
  } catch (error) {
    console.error("Error generating outline:", error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        message: "Failed to generate outline. Please try again." 
      })
    };
  }
};
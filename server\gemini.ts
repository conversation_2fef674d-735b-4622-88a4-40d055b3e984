import { GoogleGenerativeAI } from "@google/generative-ai";
import { OutlineGenerationParams, ContentGenerationParams, BookTitle, BookLanguage, TargetAudience } from "@shared/types";

// Initialize the API with the API key
const API_KEY = process.env.GEMINI_API_KEY;
if (!API_KEY) {
  throw new Error("GEMINI_API_KEY environment variable is required");
}
const MODEL = "gemini-2.0-flash";

const genAI = new GoogleGenerativeAI(API_KEY);
const model = genAI.getGenerativeModel({ model: MODEL });

// Helper function to create tone/style descriptions for prompts
function getToneDescription(tone: string): string {
  const toneDescriptions = {
    professional: "formal, authoritative, and business-like",
    casual: "relaxed, conversational, and friendly",
    academic: "scholarly, rigorous, and evidence-based",
    conversational: "engaging, relatable, and approachable",
    instructional: "clear, directive, and educational"
  };
  return toneDescriptions[tone as keyof typeof toneDescriptions] || toneDescriptions.professional;
}

function getStyleDescription(style: string): string {
  const styleDescriptions = {
    descriptive: "rich in detail and imagery",
    analytical: "focused on breaking down concepts and examining relationships",
    persuasive: "aimed at convincing the reader with compelling arguments",
    narrative: "story-driven with clear flow and progression",
    technical: "precise, detailed, and specialized"
  };
  return styleDescriptions[style as keyof typeof styleDescriptions] || styleDescriptions.descriptive;
}

function getLanguageDescription(language: string): string {
  const languageDescriptions = {
    simple: "using straightforward vocabulary and short sentences accessible to a wide audience",
    intermediate: "balancing clarity with some field-specific terminology",
    advanced: "using sophisticated vocabulary and complex sentence structures",
    technical: "employing specialized terminology and jargon appropriate for experts"
  };
  return languageDescriptions[language as keyof typeof languageDescriptions] || languageDescriptions.intermediate;
}

function getTargetLanguageName(languageCode: BookLanguage): string {
  const languageNames: Record<BookLanguage, string> = {
    'ar': 'Arabic',
    'bn': 'Bengali',
    'bg': 'Bulgarian',
    'zh-CN': 'Chinese (Simplified)',
    'zh-TW': 'Chinese (Traditional)',
    'hr': 'Croatian',
    'cs': 'Czech',
    'da': 'Danish',
    'nl': 'Dutch',
    'en': 'English',
    'et': 'Estonian',
    'fi': 'Finnish',
    'fr': 'French',
    'de': 'German',
    'el': 'Greek',
    'he': 'Hebrew',
    'hi': 'Hindi',
    'hu': 'Hungarian',
    'id': 'Indonesian',
    'it': 'Italian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'no': 'Norwegian',
    'pl': 'Polish',
    'pt': 'Portuguese',
    'ro': 'Romanian',
    'ru': 'Russian',
    'sr': 'Serbian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'es': 'Spanish',
    'sw': 'Swahili',
    'sv': 'Swedish',
    'th': 'Thai',
    'tr': 'Turkish',
    'uk': 'Ukrainian',
    'vi': 'Vietnamese'
  };
  return languageNames[languageCode] || 'English';
}

// Helper function to provide language-specific guidance for natural, contemporary writing
function getLanguageNaturalnessGuidance(languageCode: BookLanguage): string {
  const guidance: Record<BookLanguage, string> = {
    'bn': `
CRITICAL LANGUAGE GUIDANCE FOR BENGALI:
- Use modern colloquial Bengali (আধুনিক কথ্য বাংলা) that contemporary Bengali speakers actually use
- Avoid overly Sanskritized vocabulary unless absolutely necessary for technical terms
- Use contemporary expressions, idioms, and phrases that are common in modern Bengali conversation and media
- Write in a natural flow that matches how educated Bengali speakers write today (newspapers, blogs, modern literature)
- Avoid archaic literary forms (প্রাচীন সাহিত্যিক রূপ) - use modern standard Bengali instead
- Use simple, direct sentence structures that feel conversational yet professional
- Include commonly used English loanwords where they're naturally integrated into modern Bengali
- Match the rhythm and cadence of contemporary Bengali writing, not classical literature`,

    'hi': `
CRITICAL LANGUAGE GUIDANCE FOR HINDI:
- Use modern standard Hindi (आधुनिक मानक हिंदी) with contemporary vocabulary that Hindi speakers actually use today
- Balance pure Hindi with commonly accepted Urdu and English loanwords that are part of modern Hindi usage
- Avoid overly Sanskrit-heavy language unless required for technical precision
- Use expressions and phrases that are common in modern Hindi media, conversation, and contemporary writing
- Write in a natural, flowing style that matches how educated Hindi speakers communicate today
- Avoid archaic or overly formal literary language - aim for modern, accessible Hindi
- Use sentence structures that feel natural and conversational while maintaining professionalism
- Include vocabulary that reflects current Hindi usage patterns, not textbook or classical Hindi`,

    'ar': `
LANGUAGE GUIDANCE FOR ARABIC:
- Use modern standard Arabic (الفصحى العصرية) that is accessible to contemporary Arabic speakers
- Avoid overly classical or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Arabic media and contemporary writing
- Write in a clear, direct style that feels natural to modern Arabic readers`,

    'zh-CN': `
LANGUAGE GUIDANCE FOR CHINESE (SIMPLIFIED):
- Use contemporary Mandarin Chinese that reflects modern usage patterns
- Avoid overly formal or classical expressions unless necessary
- Use vocabulary and sentence structures common in modern Chinese writing and media
- Write in a natural style that contemporary Chinese speakers would find engaging and accessible`,

    'zh-TW': `
LANGUAGE GUIDANCE FOR CHINESE (TRADITIONAL):
- Use contemporary Traditional Chinese that reflects modern Taiwan/Hong Kong usage
- Avoid overly formal or classical expressions unless necessary
- Use vocabulary and expressions common in modern Traditional Chinese media and writing
- Write in a natural style that contemporary Traditional Chinese speakers would find engaging`,

    'es': `
LANGUAGE GUIDANCE FOR SPANISH:
- Use contemporary, international Spanish that is accessible across different Spanish-speaking regions
- Avoid overly formal or archaic expressions unless necessary
- Use modern vocabulary and expressions that are common in contemporary Spanish writing and media
- Write in a natural, engaging style that feels conversational yet professional`,

    'fr': `
LANGUAGE GUIDANCE FOR FRENCH:
- Use contemporary French that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern French writing and media
- Write in a natural style that contemporary French speakers would find engaging and accessible`,

    'de': `
LANGUAGE GUIDANCE FOR GERMAN:
- Use contemporary German that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and sentence structures common in modern German writing and media
- Write in a natural style that contemporary German speakers would find engaging`,

    'ja': `
LANGUAGE GUIDANCE FOR JAPANESE:
- Use contemporary Japanese that reflects modern usage patterns
- Balance formal and casual elements appropriately for the content type
- Use vocabulary and expressions common in modern Japanese writing and media
- Write in a natural style that contemporary Japanese speakers would find engaging and accessible`,

    'ko': `
LANGUAGE GUIDANCE FOR KOREAN:
- Use contemporary Korean that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Korean writing and media
- Write in a natural style that contemporary Korean speakers would find engaging`,

    'pt': `
LANGUAGE GUIDANCE FOR PORTUGUESE:
- Use contemporary Portuguese that is accessible to both Brazilian and European Portuguese speakers
- Avoid overly formal or archaic expressions unless necessary
- Use modern vocabulary and expressions common in contemporary Portuguese writing and media
- Write in a natural, engaging style that feels conversational yet professional`,

    'ru': `
LANGUAGE GUIDANCE FOR RUSSIAN:
- Use contemporary Russian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Russian writing and media
- Write in a natural style that contemporary Russian speakers would find engaging and accessible`,

    'it': `
LANGUAGE GUIDANCE FOR ITALIAN:
- Use contemporary Italian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Italian writing and media
- Write in a natural style that contemporary Italian speakers would find engaging`,

    'tr': `
LANGUAGE GUIDANCE FOR TURKISH:
- Use contemporary Turkish that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Turkish writing and media
- Write in a natural style that contemporary Turkish speakers would find engaging and accessible`,

    'th': `
LANGUAGE GUIDANCE FOR THAI:
- Use contemporary Thai that reflects modern usage patterns
- Balance formal and informal elements appropriately for the content type
- Use vocabulary and expressions common in modern Thai writing and media
- Write in a natural style that contemporary Thai speakers would find engaging`,

    'vi': `
LANGUAGE GUIDANCE FOR VIETNAMESE:
- Use contemporary Vietnamese that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Vietnamese writing and media
- Write in a natural style that contemporary Vietnamese speakers would find engaging and accessible`,

    'uk': `
LANGUAGE GUIDANCE FOR UKRAINIAN:
- Use contemporary Ukrainian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Ukrainian writing and media
- Write in a natural style that contemporary Ukrainian speakers would find engaging`,

    'pl': `
LANGUAGE GUIDANCE FOR POLISH:
- Use contemporary Polish that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Polish writing and media
- Write in a natural style that contemporary Polish speakers would find engaging and accessible`,

    'en': '', // No special guidance needed for English
    'bg': `
LANGUAGE GUIDANCE FOR BULGARIAN:
- Use contemporary Bulgarian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Bulgarian writing and media
- Write in a natural style that contemporary Bulgarian speakers would find engaging and accessible`,

    'hr': `
LANGUAGE GUIDANCE FOR CROATIAN:
- Use contemporary Croatian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Croatian writing and media
- Write in a natural style that contemporary Croatian speakers would find engaging and accessible`,

    'cs': `
LANGUAGE GUIDANCE FOR CZECH:
- Use contemporary Czech that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Czech writing and media
- Write in a natural style that contemporary Czech speakers would find engaging and accessible`,

    'da': `
LANGUAGE GUIDANCE FOR DANISH:
- Use contemporary Danish that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Danish writing and media
- Write in a natural style that contemporary Danish speakers would find engaging and accessible`,

    'nl': `
LANGUAGE GUIDANCE FOR DUTCH:
- Use contemporary Dutch that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Dutch writing and media
- Write in a natural style that contemporary Dutch speakers would find engaging and accessible`,

    'et': `
LANGUAGE GUIDANCE FOR ESTONIAN:
- Use contemporary Estonian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Estonian writing and media
- Write in a natural style that contemporary Estonian speakers would find engaging and accessible`,

    'fi': `
LANGUAGE GUIDANCE FOR FINNISH:
- Use contemporary Finnish that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Finnish writing and media
- Write in a natural style that contemporary Finnish speakers would find engaging and accessible`,

    'el': `
LANGUAGE GUIDANCE FOR GREEK:
- Use contemporary Greek that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Greek writing and media
- Write in a natural style that contemporary Greek speakers would find engaging and accessible`,

    'he': `
LANGUAGE GUIDANCE FOR HEBREW:
- Use contemporary Hebrew that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Hebrew writing and media
- Write in a natural style that contemporary Hebrew speakers would find engaging and accessible`,

    'hu': `
LANGUAGE GUIDANCE FOR HUNGARIAN:
- Use contemporary Hungarian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Hungarian writing and media
- Write in a natural style that contemporary Hungarian speakers would find engaging and accessible`,

    'id': `
LANGUAGE GUIDANCE FOR INDONESIAN:
- Use contemporary Indonesian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Indonesian writing and media
- Write in a natural style that contemporary Indonesian speakers would find engaging and accessible`,

    'lv': `
LANGUAGE GUIDANCE FOR LATVIAN:
- Use contemporary Latvian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Latvian writing and media
- Write in a natural style that contemporary Latvian speakers would find engaging and accessible`,

    'lt': `
LANGUAGE GUIDANCE FOR LITHUANIAN:
- Use contemporary Lithuanian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Lithuanian writing and media
- Write in a natural style that contemporary Lithuanian speakers would find engaging and accessible`,

    'no': `
LANGUAGE GUIDANCE FOR NORWEGIAN:
- Use contemporary Norwegian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Norwegian writing and media
- Write in a natural style that contemporary Norwegian speakers would find engaging and accessible`,

    'ro': `
LANGUAGE GUIDANCE FOR ROMANIAN:
- Use contemporary Romanian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Romanian writing and media
- Write in a natural style that contemporary Romanian speakers would find engaging and accessible`,

    'sr': `
LANGUAGE GUIDANCE FOR SERBIAN:
- Use contemporary Serbian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Serbian writing and media
- Write in a natural style that contemporary Serbian speakers would find engaging and accessible`,

    'sk': `
LANGUAGE GUIDANCE FOR SLOVAK:
- Use contemporary Slovak that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Slovak writing and media
- Write in a natural style that contemporary Slovak speakers would find engaging and accessible`,

    'sl': `
LANGUAGE GUIDANCE FOR SLOVENIAN:
- Use contemporary Slovenian that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Slovenian writing and media
- Write in a natural style that contemporary Slovenian speakers would find engaging and accessible`,

    'sw': `
LANGUAGE GUIDANCE FOR SWAHILI:
- Use contemporary Swahili that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Swahili writing and media
- Write in a natural style that contemporary Swahili speakers would find engaging and accessible`,

    'sv': `
LANGUAGE GUIDANCE FOR SWEDISH:
- Use contemporary Swedish that reflects modern usage patterns
- Avoid overly formal or archaic expressions unless necessary
- Use vocabulary and expressions common in modern Swedish writing and media
- Write in a natural style that contemporary Swedish speakers would find engaging and accessible`
  };

  return guidance[languageCode] || '';
}

export async function generateBookTitles(
  userInput: string,
  params?: OutlineGenerationParams
): Promise<BookTitle[]> {
  try {
    const targetLanguage = params?.targetLanguage ? getTargetLanguageName(params.targetLanguage) : "English";
    const languageGuidance = params?.targetLanguage ? getLanguageNaturalnessGuidance(params.targetLanguage) : "";

    // Build audience guidance if target audience is specified
    let audienceGuidance = "";
    if (params?.targetAudience && params.targetAudience !== "General Audience") {
      audienceGuidance = `\n- Consider ${params.targetAudience} as a potential audience, but generate diverse audience targets`;
    }

    // Get book size constraints
    const bookSize = params?.bookSize || 'small';
    let sizeGuidance = "";
    switch (bookSize) {
      case 'small':
        sizeGuidance = `\n- BOOK SIZE: Small (5-10 chapters with 15-30 total sub-chapters across all chapters)`;
        break;
      case 'medium':
        sizeGuidance = `\n- BOOK SIZE: Medium (10-20 chapters with 30-50 total sub-chapters across all chapters)`;
        break;
      case 'large':
        sizeGuidance = `\n- BOOK SIZE: Large (20-30 chapters with 50-80 total sub-chapters across all chapters)`;
        break;
    }

    const prompt = `
    You are an expert book title generator and content strategist for non-fiction Amazon KDP books.

    STEP 1: DEEP ANALYSIS
    Perform a comprehensive analysis of this topic: "${userInput}"
    - Analyze the core themes, pain points, and opportunities
    - Identify different angles and approaches to the subject
    - Consider various audience segments who would benefit
    - Assess the emotional and practical appeal

    STEP 2: GENERATE 6 DIVERSE BOOK CONCEPTS
    Create exactly 6 unique book titles with complete parameters. Each book should represent a different approach to the topic.

    IMPORTANT: Generate all content in ${targetLanguage}. All text must be written in ${targetLanguage}.
    ${languageGuidance}

    LANGUAGE QUALITY REQUIREMENTS FOR TITLES:
    - Use contemporary, natural language that native speakers actually use in modern writing
    - Avoid archaic, overly formal, or textbook-style language for book titles
    - Create titles that feel authentic and engaging to native speakers
    - Use vocabulary and expressions that are common in current, everyday usage
    - Ensure titles sound natural and appealing to modern readers

    Requirements for each book:
    - Title: 3-8 words, attention-grabbing and professional
    - Chapter count and sub-chapter structure MUST fit within the specified book size constraints${sizeGuidance}
    - Different tones: professional, casual, academic, conversational, instructional
    - Different styles: descriptive, analytical, persuasive, narrative, technical
    - Different language levels: simple, intermediate, advanced, technical
    - Diverse target audiences: business professionals, students, general readers, entrepreneurs, etc.
    - 2-3 line summary explaining the book's purpose and unique value${audienceGuidance}

    ENSURE VARIETY: Each book should feel distinctly different in approach, complexity, and target market.
    CRITICAL: All chapter counts and sub-chapter distributions MUST respect the ${bookSize} book size constraints.

    Format your response as a JSON array with this exact structure:
    [
      {
        "id": "1",
        "title": "Book Title Here",
        "description": "Brief description of what this title offers",
        "chapterCount": 8,
        "subChapterCounts": [3, 4, 3, 5, 2, 4, 3, 4],
        "tone": "professional",
        "style": "analytical",
        "languageLevel": "intermediate",
        "targetAudience": "Business professionals",
        "summary": "This book provides a comprehensive framework for understanding [topic]. It combines practical strategies with real-world examples to help readers achieve measurable results in their professional endeavors."
      }
    ]

    Only return the JSON array, no additional text.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error("Failed to generate valid JSON titles");
    }

    const parsedTitles = JSON.parse(jsonMatch[0]);

    if (!Array.isArray(parsedTitles) || parsedTitles.length === 0) {
      throw new Error("Invalid titles structure");
    }

    // Import book size utilities for validation
    const { generateSizeAwareStructure, getBookSizeConstraints } = await import('../shared/bookSizeUtils');
    const constraints = getBookSizeConstraints(bookSize);

    // Validate that each title has all required fields and respects book size constraints
    const validatedTitles = parsedTitles.map((title, index) => {
      // Generate size-appropriate structure if AI didn't provide valid values
      let chapterCount = title.chapterCount || 6;
      let subChapterCounts = Array.isArray(title.subChapterCounts) ? title.subChapterCounts : [3, 3, 3, 3, 3, 3];

      // Validate chapter count is within constraints
      if (chapterCount < constraints.minChapters || chapterCount > constraints.maxChapters) {
        const sizeAwareStructure = generateSizeAwareStructure(bookSize);
        chapterCount = sizeAwareStructure.chapterCount;
        subChapterCounts = sizeAwareStructure.subChapterCounts;
      } else {
        // Validate total sub-chapters
        const totalSubChapters = subChapterCounts.reduce((sum: number, count: number) => sum + count, 0);
        if (totalSubChapters < constraints.minSubChapters || totalSubChapters > constraints.maxSubChapters) {
          const sizeAwareStructure = generateSizeAwareStructure(bookSize);
          chapterCount = sizeAwareStructure.chapterCount;
          subChapterCounts = sizeAwareStructure.subChapterCounts;
        }
      }

      return {
        id: title.id || String(index + 1),
        title: title.title || "Untitled Book",
        description: title.description || "",
        chapterCount,
        subChapterCounts,
        tone: title.tone || "professional",
        style: title.style || "descriptive",
        languageLevel: title.languageLevel || "intermediate",
        targetAudience: title.targetAudience || "General readers",
        summary: title.summary || "A comprehensive guide to the topic."
      };
    });

    return validatedTitles;
  } catch (error) {
    console.error("Error in generateBookTitles:", error);
    throw new Error("Failed to generate book titles");
  }
}

export async function generateOutline(
  userInput: string, 
  params: OutlineGenerationParams
): Promise<{
  chapters: Array<{ title: string; subchapters: string[] }>;
}> {
  try {
    // Build outline generation parameters from user settings
    const toneDescription = params.tone ? getToneDescription(params.tone) : "";
    const styleDescription = params.style ? getStyleDescription(params.style) : "";
    const languageDescription = params.language ? getLanguageDescription(params.language) : "";
    const targetLanguage = params.targetLanguage ? getTargetLanguageName(params.targetLanguage) : "English";
    const languageGuidance = params.targetLanguage ? getLanguageNaturalnessGuidance(params.targetLanguage) : "";

    // Optional writing style guidance section
    let styleGuidance = "";
    if (params.tone || params.style || params.language) {
      styleGuidance = `
      Writing style guidance:
      ${params.tone ? `- Use a ${toneDescription} tone` : ""}
      ${params.style ? `- Write in a ${styleDescription} style` : ""}
      ${params.language ? `- Use ${languageDescription} language level` : ""}
      `;
    }
    
    // Build audience guidance if target audience is specified
    let audienceGuidance = "";
    if (params.targetAudience && params.targetAudience !== "General Audience") {
      audienceGuidance = `
      Target Audience Guidance:
      - Tailor content complexity and examples for: ${params.targetAudience}
      - Use language and references appropriate for this audience
      - Structure chapters to match audience knowledge level and interests
      `;
    }

    // Build book summary guidance if available
    let summaryGuidance = "";
    if (params.summary) {
      summaryGuidance = `
      Book Summary Context:
      "${params.summary}"
      
      CRITICAL: Ensure the outline directly supports and delivers on the promises made in this summary.
      - Each chapter should contribute to achieving the book's stated purpose
      - Sub-chapters should break down the key concepts mentioned in the summary
      - Maintain logical flow that leads to the outcomes described in the summary
      - Include content that substantiates the claims and value propositions in the summary
      `;
    }

    // Build chapter structure guidance based on available parameters
    let chapterStructureGuidance = "";
    if (params.subChapterCounts && params.subChapterCounts.length > 0) {
      // Use specific sub-chapter counts for dynamic distribution
      chapterStructureGuidance = `
    The outline should have exactly ${params.maxChapters} main chapters with the following specific sub-chapter distribution:
    ${params.subChapterCounts.map((count, index) => `Chapter ${index + 1}: ${count} sub-chapters`).join('\n    ')}

    IMPORTANT: Follow this exact structure - each chapter must have the specified number of sub-chapters.`;
    } else {
      // Fall back to uniform distribution for backward compatibility
      chapterStructureGuidance = `
    The outline should have up to ${params.maxChapters} main chapters with up to ${params.maxSubChapters} subchapters each.`;
    }

    const prompt = `
    You are an expert eBook outliner for non-fiction books. Create a detailed, hierarchical outline for a book on the following topic: "${userInput}".

    ${summaryGuidance}

    ${chapterStructureGuidance}

    IMPORTANT: Generate all content in ${targetLanguage}. All chapter titles and subchapter titles must be written in ${targetLanguage}.
    ${languageGuidance}

    LANGUAGE QUALITY REQUIREMENTS FOR TITLES:
    - Use contemporary, natural language that native speakers actually use in modern writing
    - Avoid archaic, overly formal, or textbook-style language for chapter and subchapter titles
    - Create titles that feel authentic and engaging to native speakers
    - Use vocabulary and expressions that are common in current, everyday usage
    - Ensure titles sound natural and appealing to modern readers

    ${styleGuidance}${audienceGuidance}

    Format your response strictly as a JSON object with the following structure:
    {
      "chapters": [
        {
          "title": "Chapter Title",
          "subchapters": ["Subchapter 1 Title", "Subchapter 2 Title", "Subchapter 3 Title"]
        }
      ]
    }

    Ensure the book follows a logical progression, starting with foundational concepts and moving toward more advanced topics. Each chapter should have a clear purpose, and subchapters should break down the main ideas in a sequential manner.

    Only return the JSON structure, no additional text or explanation.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Extract JSON from the response (in case there's extra text)
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("Failed to generate valid JSON outline");
    }

    const parsedOutline = JSON.parse(jsonMatch[0]);
    
    // Validate the structure
    if (!parsedOutline.chapters || !Array.isArray(parsedOutline.chapters)) {
      throw new Error("Invalid outline structure");
    }

    return parsedOutline;
  } catch (error) {
    console.error("Error in generateOutline:", error);
    throw new Error("Failed to generate book outline");
  }
}

export async function generateChapterContent(
  subChapterTitle: string,
  mainChapterTitle: string,
  bookTopic: string,
  params: ContentGenerationParams
): Promise<string> {
  try {
    // Get descriptions for content generation
    const toneDescription = getToneDescription(params.tone);
    const styleDescription = getStyleDescription(params.style);
    const languageDescription = getLanguageDescription(params.language);
    const targetLanguage = params.targetLanguage ? getTargetLanguageName(params.targetLanguage) : "English";
    const languageGuidance = params.targetLanguage ? getLanguageNaturalnessGuidance(params.targetLanguage) : "";

    // Build audience context if target audience is specified
    const audienceContext = params.targetAudience && params.targetAudience !== "General Audience"
      ? `\n\nTarget Audience: ${params.targetAudience}\n- Adapt examples, complexity, and tone for this specific audience\n- Use terminology and references relevant to their background and interests`
      : "";

    const prompt = `
    You are an expert non-fiction book writer for Amazon KDP. Write detailed, informative content for the following subchapter:

    Book Topic: "${bookTopic}"
    Main Chapter: "${mainChapterTitle}"
    Subchapter: "${subChapterTitle}"

    IMPORTANT: Write all content in ${targetLanguage}. The entire chapter content must be written in ${targetLanguage}.
    ${languageGuidance}

    Writing Style:
    - Tone: ${toneDescription}
    - Style: ${styleDescription}
    - Language Level: ${languageDescription}${audienceContext}

    LANGUAGE QUALITY REQUIREMENTS:
    - Use contemporary, natural language that native speakers actually use in modern writing
    - Avoid archaic, overly formal, or textbook-style language unless specifically required
    - Write in a style that feels authentic and engaging to native speakers
    - Use vocabulary and expressions that are common in current, everyday usage
    - Ensure the writing flows naturally and sounds like how educated native speakers would write today

    Guidelines:
    - Write approximately 800-1200 words of polished, publication-ready content
    - Include relevant headings where appropriate (using markdown format: # ## ###)
    - Include practical examples, actionable advice, and evidence-based information
    - Format the text with clear paragraphs, occasional bullet points for key points, and proper structure
    - Ensure this subchapter flows logically while being able to stand alone
    - Tailor examples and complexity level specifically for the target audience

    FORMATTING REQUIREMENTS:
    - DO NOT wrap the content in code blocks (no \`\`\`markdown or \`\`\`)
    - Use clean markdown formatting only
    - For bullet points, use "* " (asterisk followed by space) at the start of each line
    - Ensure each bullet point is on its own line
    - Use proper line breaks between sections
    - Do not use extra asterisks (*) or hash symbols (#) outside of proper markdown formatting

    Return only the clean subchapter content with proper markdown formatting. Do not include any code block markers.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    let text = response.text();

    // Clean up any remaining markdown artifacts
    text = text
      // Remove markdown code block markers
      .replace(/```markdown\s*/gi, '')
      .replace(/```\s*/g, '')
      // Remove extra asterisks that aren't part of formatting
      .replace(/\*{3,}/g, '')
      // Remove extra hash symbols that aren't headers
      .replace(/#{4,}/g, '###')
      // Clean up any double spaces
      .replace(/  +/g, ' ')
      // Ensure proper line breaks after bullet points
      .replace(/(\* [^\n]*)\s*(\* [^\n]*)/g, '$1\n\n$2')
      // Clean up excessive line breaks
      .replace(/\n{4,}/g, '\n\n\n')
      .trim();

    return text;
  } catch (error) {
    console.error("Error in generateChapterContent:", error);
    throw new Error("Failed to generate chapter content");
  }
}

import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy, 
  Timestamp,
  DocumentData 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { SavedProject, CreateProjectData } from '@shared/types';

const PROJECTS_COLLECTION = 'projects';

export class ProjectService {
  /**
   * Save a new project to Firestore
   */
  static async saveProject(userId: string, projectData: CreateProjectData): Promise<string> {
    try {
      const projectToSave = {
        ...projectData,
        userId,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      const docRef = await addDoc(collection(db, PROJECTS_COLLECTION), projectToSave);
      return docRef.id;
    } catch (error) {
      console.error('Error saving project:', error);
      throw new Error('Failed to save project');
    }
  }

  /**
   * Update an existing project
   */
  static async updateProject(projectId: string, updates: Partial<CreateProjectData>): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        ...updates,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating project:', error);
      throw new Error('Failed to update project');
    }
  }

  /**
   * Get all projects for a specific user
   */
  static async getUserProjects(userId: string): Promise<SavedProject[]> {
    try {
      const q = query(
        collection(db, PROJECTS_COLLECTION),
        where('userId', '==', userId),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const projects: SavedProject[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        projects.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
        } as SavedProject);
      });

      return projects;
    } catch (error) {
      console.error('Error fetching user projects:', error);
      throw new Error('Failed to fetch projects');
    }
  }

  /**
   * Get a specific project by ID
   */
  static async getProject(projectId: string): Promise<SavedProject | null> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      const projectSnap = await getDoc(projectRef);

      if (!projectSnap.exists()) {
        return null;
      }

      const data = projectSnap.data();
      return {
        id: projectSnap.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as SavedProject;
    } catch (error) {
      console.error('Error fetching project:', error);
      throw new Error('Failed to fetch project');
    }
  }

  /**
   * Delete a project
   */
  static async deleteProject(projectId: string): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await deleteDoc(projectRef);
    } catch (error) {
      console.error('Error deleting project:', error);
      throw new Error('Failed to delete project');
    }
  }

  /**
   * Update project title
   */
  static async updateProjectTitle(projectId: string, newTitle: string): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        title: newTitle,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating project title:', error);
      throw new Error('Failed to update project title');
    }
  }

  /**
   * Update project status
   */
  static async updateProjectStatus(projectId: string, status: SavedProject['status']): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        status,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating project status:', error);
      throw new Error('Failed to update project status');
    }
  }

  /**
   * Update generated chapters for a project
   */
  static async updateGeneratedChapters(projectId: string, generatedChapters: Record<string, boolean>): Promise<void> {
    try {
      const projectRef = doc(db, PROJECTS_COLLECTION, projectId);
      await updateDoc(projectRef, {
        generatedChapters,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating generated chapters:', error);
      throw new Error('Failed to update generated chapters');
    }
  }
}
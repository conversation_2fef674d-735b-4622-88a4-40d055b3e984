analyze the full codebase and Update current theme to a semi-translucent gray glass aesthetic to get batter visuals. Also update all colors to match the UI and elements to match the UI.



Analyze the current theme configuration in `theme.json` and `tailwind.config.ts` to implement a modern semi-translucent gray glass aesthetic (glassmorphism design). Please:

1. Update the `theme.json` file to use appropriate gray-based colors with transparency values
2. Modify the Tailwind CSS configuration in `tailwind.config.ts` to support glass-like effects including:
   - Semi-transparent background colors
   - Backdrop blur utilities
   - Subtle border colors with transparency
   - Updated color palette that emphasizes grays, whites, and subtle accent colors
3. Ensure all UI components (buttons, cards, dialogs, sidebars, etc.) use colors that create a cohesive glassmorphism aesthetic
4. Update CSS custom properties to support the new glass theme
5. Maintain accessibility standards while implementing the translucent design
6. Provide specific color values and opacity levels that work well together

The goal is to achieve better visual appeal through a modern glass-like interface with consistent color harmony across all UI elements.

buttons and it's text dont match like "Get Started" "Start Writing Now" "Generate Outline" etc. button text is black and the button is gray. I want gradient style buttons with eye-catching color and matching text color. analyze the full codebase and update all other poor contrast and visual appeal.

update the "Outline Structure" functionality as follows:
> remove "Min Chapters" and keep "Max Chapters", "Max Chapters" should be renamed to "Chapters" and the input should be 10 as a default value and max of 30.
> remove "Min Sub Chapters" and keep "Max Sub Chapters", "Max Sub Chapters" should be renamed to "Sub Chapters Under Each Chapter" and the input should be 5 as a default value and max of 10.

update the "Book Outline" functionality as follows:
> in generated outline, user able to generate content from any chapter and sub-chapter but I want to limit it to only generate content sequentially. meaning user should only be able to generate content for the first sub-chapter of the first chapter, once that is generated then only the user should be able to generate content for the second sub-chapter of the first chapter, and so on.
> when user clicks on a "Sub Chapter Under Each Chapter" it generates the content and I see tick mark next to it, but when user clicks on the generated sub chapter again it generates the content again, I want to prevent this and show the generated content again if user clicks on the generated sub chapter again.

sequentially content generation is working but when the first sub-chapter of the first chapter is generated the second sub-chapter of the first chapter is still disabled style. but when I click on the second sub-chapter of the first chapter it generates correctly. fix the style of the second sub-chapter of the first chapter to be enabled style after the first sub-chapter of the first chapter is generated, and so on.

in "Book Outline" add "Generate All Sub-Chapters" buttons below the each main chapter's "Sub Chapters", when user clicks on it, it generates all the sub chapters of that main chapter, one by one, in sequence. if all the sub chapters generated in previous chapter then show the "Generate All Sub-Chapters" button enabled for the next chapter. analyze the full codebase and implement this functionality and logic properly.

"Generate All Sub-Chapters" button color should be blue gradient like other buttons with white text. while clicked on "Generate All Sub-Chapters" button it should show a loading state with text "Generating..." in the button and make the button red color. and when all the sub chapters are generated make the all "Generate All Sub-Chapters" buttons color green with text of "Generated All Sub Chapters" and show a checkmark icon next to the button.

AIeBookWriter
analyze the full codebase and add the authentication (login, signup and forgot password) form with functionality to the app using the following firebase config:
apiKey: "AIzaSyC1EPbhsqYAFPfsxBtOF1xIbU45RoA85RA",
authDomain: "aiebookwriter.firebaseapp.com",
projectId: "aiebookwriter",
storageBucket: "aiebookwriter.firebasestorage.app",
messagingSenderId: "428756360026",
appId: "1:428756360026:web:a2a3f41d2ec3da25143163",
measurementId: "G-TZFXM9KSTH"
create an .env file in the root directory of the project and add the firebase config to it.

add the following features:
# add "Google" as an authentication option
# add Disposable Email verification while signup, using https://debounce.io/free-disposable-check-api/
# Enforce email verification before /app access
# Add a “Resend verification email” banner if user not verified (sendVerification is already exposed in context)
# Add basic form validation errors inline (e.g., confirm password mismatch) beyond what’s already included
# Add a profile dropdown to Navbar (show email, verified state)


while clicking on "Profile Badge" (#root > div.min-h-screen.flex.flex-col > nav > div > div > div:nth-child(2) > div) in the navbar, showing the dropdown behind the elements in http://localhost:5000/app page. I want the dropdown to be shown on top of the everything and not behind the elements and all pages. also Add provider-specific branding to Google buttons if desired (icon, colors) while maintaining the glass theme.


still "Profile Badge" dropdown is shown behind the elements in http://localhost:5000/app page. fix the fix the issue.

after successful login or email verification, redirect user to /app page. Also rename this current app name to "AIeBookWriter.Pro"

rename the "Generate Outline" button to "Generate Book Title" and output the 10 very engaging book titles in the "No Content Selected" area. user will able to select any one of the book title and click on "Generate Outline" button to generate the outline for the selected book title. show "Generate Outline" button only after user select any one of the book title after the selected book title is highlighted.

show the "Generate Outline" button within the selected book title card in a suitable place and style.

What a student should do after graduation to build a successful career
Planting in rooftop

add "Language" dropdown in between "Generate Book Titles" and "Customization Options" with the following languages to choose from to write the book in:
Arabic (ar)
Bengali (bn)
Bulgarian (bg)
Chinese simplified (zh)
Chinese traditional (zh)
Croatian (hr)
Czech (cs)
Danish (da)
Dutch (nl)
English (en)
Estonian (et)
Finnish (fi)
French (fr)
German (de)
Greek (el)
Hebrew (iw)
Hindi (hi)
Hungarian (hu)
Indonesian (id)
Italian (it)
Japanese (ja)
Korean (ko)
Latvian (lv)
Lithuanian (lt)
Norwegian (no)
Polish (pl)
Portuguese (pt)
Romanian (ro)
Russian (ru)
Serbian (sr)
Slovak (sk)
Slovenian (sl)
Spanish (es)
Swahili (sw)
Swedish (sv)
Thai (th)
Turkish (tr)
Ukrainian (uk)
Vietnamese (vi)
implement the language functionality very wisely and effectively.

loading animation is not smooth in many places and currently loading animation is very simple. I want a premium looking and smooth loading animation. update all loading animations across the app according my requirements.

In "Customization Options" in "Outline Structure" section, add slider for "Chapters" and "Sub Chapters Under Each Chapter" along with the number input. implement the functionality very wisely and effectively with premium looking.

http://localhost:5000/app rename the app page to "Create eBook" also change the url to /create-ebook and navbar to "Create eBook"

In "Customization Options" in "Outline Structure" section, "Chapters" should be 5 as a default value & min of 5. "Sub Chapters Under Each Chapter" should be 3 as a default value & min of 3.

when clicking on generated Sub chapter from "Book Outline" section, selected Sub chapter should highlighted. implement the functionality very wisely and effectively.

analyze the full codebase and fix the following:
content output format and style not good, make the output format and style more accurate. read the @c:\Users\<USER>\OneDrive\Desktop\Cursor Full-Stack Apps\NonFictionBookBuilder/0_Docs\output_eg.md I have given 6 examples of current output format and style. In current output format and style I am getting extra *, #, sometimes some unwanted values like ```markdown, ``` etc. fix the output format and style very wisely and effectively. some time line brake does not work properly like below example:
* Visual Harmony: A theme ensures consistent use of color palettes, materials, and design elements, resulting in a visually pleasing and relaxing environment. * Functional Cohesion: The chosen theme will often dictate the functional elements. For example, a Mediterranean theme might prioritize outdoor dining and lounging, while a Zen garden theme emphasizes tranquility and contemplation. * Budget Management: A defined theme helps to focus your budget on specific items and materials that align with the overall vision, preventing impulsive purchases that might not fit the aesthetic. * Increased Enjoyment: A well-executed theme contributes to a more immersive and enjoyable experience, transporting you to the desired atmosphere whenever you step onto your rooftop.

analyze the full codebase and suggest me where I can store the generated content data online so that user can come back later and continue from where he left.

analyze the full codebase and suggest me, is it a good decision to store the generated content data to Google Firestore so that user can come back later and continue from where he left.

analyze the full codebase and tell me is there any other new functionality/feature that I can add to improve the user experience and make the app more useful and unique in "Writing Style"?

add "Target Audience" text input field  not dropdown in "Writing Style" section to ask user to input the target audience of the book, by default it should be "General Audience" and update the existing functionality and logic for the workflow accordingly. Implement the "Target Audience" functionality very wisely and effectively.

I want to update existing book title generation logic and functionality to following:
> when user click on "Generate Book Titles" button, it should deep analyze inputted data and sentiment analysis of the inputted data in "Book Topic or Keywords" then generate 5 book titles based on the analysis and sentiment. automatically generate "Chapters" and "Sub Chapters Under Each Chapter" count based on the analysis and sentiment to create a very comprehensive and detailed outline to create a very comprehensive book. also automatically generate "Tone", "Style", "Language Level" and "Target Audience" based on the analysis and sentiment, it should be unique and dynamic for each book title to create different type of books. book title card should look like book cover design style with book title as book name and below it show the generated "Chapters" and "Sub Chapters Under Each Chapter" count in small font size and "Tone", "Style", "Language Level" and "Target Audience" in even smaller font size, also generate a book summary of 2-3 lines based on the analysis and sentiment and show it below the "Target Audience" to batter explain the book what it is about. To do above things use Gemini 2.5 Flash model. when book cover is selected, it should show the selected book title card highlighted and show the "Apply and Generate Outline" button within the selected book cover card. Apply means all the generated parameters for the selected book should be applied to the Outline Structure section and Writing Style section parameters should be updated automatically. implement the functionality very wisely and effectively.

analyze the full codebase and fix the following:
when selecting a book title card and clicking on "Apply and Generate Outline" button, it does not apply the values and parameters to the "Outline Structure"s "Chapters" and "Sub Chapters Under Each Chapter" and "Writing Style" section "Tone", "Style", "Language Level" and "Target Audience" in "Writing Style" section. in "Writing Style" section, make "Tone", "Style" and "Language Level" text input fields style like "Target Audience" text input field not dropdowns. also update the "Outline Structure"s "Chapters" and "Sub Chapters Under Each Chapter" functionality and logic as required. Rename "Apply and Generate Outline" button to "Apply" and style it like other buttons with gradient blue color and white text. When click on "Apply" button, it should apply the values and parameters to the "Outline Structure"s "Chapters" and "Sub Chapters Under Each Chapter" and "Writing Style" section "Tone", "Style", "Language Level" and "Target Audience" in "Writing Style" section. implement the functionality very wisely and effectively. don's start generating outline until user click on "Generate Outline" button after applying the values and parameters.

Analyze the full codebase and implement and update the following:
add three button called "Small", "Medium" and "Large" above the "Generate Book Titles" button in "Book Topic or Keywords" section. "Small" should be between 5-10 Chapters with 15-30 Sub Chapters total, "Medium" should be 10-20 Chapters with 30-50 Sub Chapters total and "Large" should be 20-30 Chapters with 50-80 Sub Chapters total. remove the current "Outline Structure" section with all existing code and make it dynamic. make the "Chapters" and "Sub Chapters Under Each Chapter" count based on the selected button according to selected Book Concept "Chapters" and "Sub Chapters Under Each Chapter" logic count in selected book title card. implement the functionality very wisely and effectively.

analyze the full codebase and fix the following:
> when I enter Book Topic or Keywords and select book size and click on "Generate Book Titles" button, it generates everything correctly. but chapter and sub-chapter is not calculated correctly based on the selected book size. it's working like previous functionality. also when I select any book title card and click on "Apply" button, it does not apply the dynamic sub-chapter count values and parameters in "Sub-chapters per Chapter", it's working like previous static functionality. fix the issue very wisely and effectively. I have attachted the screenshot to batter understand the issue.

analyze the full codebase and fix the following:
> when I select any book title card and click on "Apply" button, it does not apply the dynamic sub-chapter count values and parameters in "Sub-chapters per Chapter", it's working like previous static functionality. as for example I have selected book card named "Living Room Reset: Declutter & Thrive" with 6 Chapters • 21 Sub-chapters, (4, 3, 5, 3, 2, 4 sub-chapters per chapter)
it should work like with total 21 sub-chapters:
chapter 1: 4 sub-chapters
chapter 2: 3 sub-chapters
chapter 3: 5 sub-chapters
chapter 4: 3 sub-chapters
chapter 5: 2 sub-chapters
chapter 6: 4 sub-chapters
but it's not working like that. it working like previous static functionality as follows with total 24 sub-chapters:
chapter 1: 4 sub-chapters
chapter 2: 4 sub-chapters
chapter 3: 4 sub-chapters
chapter 4: 4 sub-chapters
chapter 5: 4 sub-chapters
chapter 6: 4 sub-chapters
fix the issue very wisely and effectively.
see the screenshot attached to better understand the issue.

In book cover card design should look like real 2d stye closed hardcover book design style.

analyze the full codebase and tell is those .git .local .netlify .qoder folders needed to run this app or I can remove them? 

ইংলিশ ছাড়া অন্যান্য ভাষায় যেমন বাংলা হিন্দি ইত্যাদি ধরনের ল্যাঙ্গুয়েজ এর ক্ষেত্রে 
Contexts does not convey the writing style and tone of native language. সেন্টেন্স এর স্ট্রাকচার ঠিক থাকলেও কিছু ক্ষেত্রে শব্দচয়ন হয়ে যাচ্ছে ওল্ড স্টাইল যা এখন আর এই ল্যাঙ্গুয়েজ এর প্রত্যাহিত জীবনের কথোপকথনে এবং লেখালেখির ক্ষেত্রে বর্তমান সময়ের লেখালেখিতে বাস্তবে ব্যবহার হয় না।  এই সমস্যার সমাধানে বুদ্ধিদীপ্ত ডিসিশন নেয়ার মাধ্যমে প্রম্পট ইঞ্জিনিয়ারিং করে exist prompts এগুলোকে আপডেট করতে চাই। create a sloid and wise plan to fix this issue.

# make passive income by selling eBook online and secrete online marketing techniques with tools
# make passive income by drop shipping business

analyze the full codebase and find out the prompt engineering workflow how actually it's working. also create a marmade flowchart to show the workflow.

What metadata/parameters is being used to generate an outline from the selected title after selecting a book title card and clicking on "Apply" button then clicking on "Generate Outline" button?

if I pass/use Book summary "selectedTitle" parameter data in "generationParams" while calling "generate-outline" API, will it help AI to generate more accurate and relevant outline,    chapter, sub-chapter and content?

{
  id: string;
  title: string;
  description?: string;
  chapterCount: number;
  subChapterCounts: number[]; // Array showing sub-chapters for each main chapter
  tone: WritingTone;
  style: WritingStyle;
  languageLevel: WritingLanguage;
  targetAudience: TargetAudience;
  summary: string; // 2-3 line book summary
}

pass/use summary: string; parameter data in "OutlineGenerationParams"  while calling "generate-outline" to  help AI to generate more accurate and relevant outline, chapter, sub-chapter and content. also add "Summary" input field in "Writing Style" section to show the book summary. update the following features very wisely and effectively.


{"functions": [{"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\netlify\\functions\\generate-chapter.ts", "name": "generate-chapter", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\.netlify\\functions\\generate-chapter.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\netlify\\functions\\generate-outline.ts", "name": "generate-outline", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\.netlify\\functions\\generate-outline.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\netlify\\functions\\generate-titles.ts", "name": "generate-titles", "priority": 10, "runtimeVersion": "nodejs20.x", "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Cursor Full-Stack Apps\\NonFictionBookBuilder\\.netlify\\functions\\generate-titles.zip", "runtime": "js"}], "system": {"arch": "x64", "platform": "win32"}, "timestamp": 1756661429852, "version": 1}
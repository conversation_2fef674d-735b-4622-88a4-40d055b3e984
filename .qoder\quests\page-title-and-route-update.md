# Page Title and Route Update Design

## Overview

This design outlines the changes required to update the NonFictionBookBuilder application's main route from `/app` to `/create-ebook` and update the page title from "app" to "Create eBook". The changes will ensure consistent branding and more descriptive navigation throughout the application.

## Current State Analysis

### Current Route Structure
- Primary application route: `/app` (protected)
- Navigation displays: "App" 
- URL accessibility: `http://localhost:5000/app`

### Current Components Affected
- **Frontend Routing**: Uses `wouter` library for client-side routing
- **Backend**: Express.js server with Vite middleware for SPA handling
- **Navigation**: Navbar component with navigation links
- **Authentication Flow**: Protected route redirects and authentication context

## Architecture Overview

```mermaid
graph TD
    A[User Request] --> B{Route: /create-ebook}
    B --> C[Wouter Router]
    C --> D[ProtectedRoute Component]
    D --> E{Authentication Check}
    E -->|Authenticated & Verified| F[AppPage Component]
    E -->|Not Authenticated| G[Redirect to /login]
    E -->|Not Verified| H[Redirect to /verify-pending]
    
    I[Navbar Component] --> J[Create eBook Link]
    J --> K[Navigate to /create-ebook]
    
    L[AuthContext] --> M[Automatic Redirects]
    M --> N[Redirect to /create-ebook]
```

## Component Changes Required

### Frontend Route Configuration

#### App.tsx Router Updates
**Current Route Definition:**
```typescript
<Route
  path="/app"
  component={() => (
    <ProtectedRoute>
      <AppPage />
    </ProtectedRoute>
  )}
/>
```

**Updated Route Definition:**
```typescript
<Route
  path="/create-ebook"
  component={() => (
    <ProtectedRoute>
      <AppPage />
    </ProtectedRoute>
  )}
/>
```

### Navigation Component Updates

#### Navbar.tsx Link Updates
**Current Navigation Link:**
```typescript
<Link href="/app">
  <a className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">App</a>
</Link>
```

**Updated Navigation Link:**
```typescript
<Link href="/create-ebook">
  <a className="ml-4 px-3 py-2 text-sm font-medium hover:text-primary transition-colors">Create eBook</a>
</Link>
```

### Authentication Context Updates

#### AuthContext.tsx Redirect Updates
**Current Redirects:**
```typescript
setLocation("/app"); // Line 46
```

**Updated Redirects:**
```typescript
setLocation("/create-ebook");
```

### Page Component Updates

#### HomePage.tsx Navigation Updates
**Current Navigation Logic:**
```typescript
setLocation("/app"); // Line 7
```

**Updated Navigation Logic:**
```typescript
setLocation("/create-ebook");
```

#### VerificationPendingPage.tsx Updates
**Current Post-Verification Redirect:**
```typescript
setLocation("/app"); // Line 19
```

**Updated Post-Verification Redirect:**
```typescript
setLocation("/create-ebook");
```

## Backend Considerations

### Server-Side Routing
The backend uses a catch-all route pattern that serves the React SPA for all non-API routes. No changes required to server-side routing as it automatically handles the new `/create-ebook` route through the SPA routing mechanism.

### Vite Development Server
The Vite middleware configuration in `server/vite.ts` uses a wildcard pattern to serve the React application, which will automatically support the new route without changes.

## Data Flow Updates

### Authentication Flow
```mermaid
sequenceDiagram
    participant User
    participant AuthContext
    participant Router
    participant ProtectedRoute
    participant AppPage

    User->>AuthContext: Login Success
    AuthContext->>Router: setLocation("/create-ebook")
    Router->>ProtectedRoute: Route to /create-ebook
    ProtectedRoute->>ProtectedRoute: Check Authentication
    ProtectedRoute->>AppPage: Render Main App
```

### Navigation Flow
```mermaid
sequenceDiagram
    participant User
    participant Navbar
    participant Router
    participant ProtectedRoute

    User->>Navbar: Click "Create eBook"
    Navbar->>Router: Navigate to /create-ebook
    Router->>ProtectedRoute: Route Handler
    ProtectedRoute->>ProtectedRoute: Verify Authentication
    ProtectedRoute->>User: Render AppPage or Redirect
```

## Implementation Strategy

### Phase 1: Route Definition Updates
1. Update primary route in `App.tsx` from `/app` to `/create-ebook`
2. Verify protected route wrapper remains intact

### Phase 2: Navigation Updates
1. Update Navbar link text from "App" to "Create eBook"
2. Update Navbar href from `/app` to `/create-ebook`

### Phase 3: Authentication Flow Updates
1. Update AuthContext redirect logic
2. Update HomePage navigation logic
3. Update VerificationPendingPage redirect logic

### Phase 4: Testing and Validation
1. Test authentication flow with new route
2. Verify all navigation links work correctly
3. Test direct URL access to `/create-ebook`
4. Verify old `/app` route returns 404 or redirects appropriately

## File Modification Summary

| File | Change Type | Description |
|------|-------------|-------------|
| `client/src/App.tsx` | Route Update | Change route path from `/app` to `/create-ebook` |
| `client/src/components/Navbar.tsx` | Navigation Link | Update href and text for main app link |
| `client/src/context/AuthContext.tsx` | Redirect Logic | Update automatic redirect destination |
| `client/src/pages/HomePage.tsx` | Navigation Logic | Update navigation target |
| `client/src/pages/auth/VerificationPendingPage.tsx` | Redirect Logic | Update post-verification redirect |

## Backward Compatibility

### Route Deprecation
The old `/app` route will no longer be accessible after the update. Consider implementing a redirect from `/app` to `/create-ebook` for transition period if needed.

### Browser History
Users with `/app` bookmarks will need to update them manually or a redirect mechanism should be implemented.

## Testing Strategy

### Unit Testing Focus Areas
- Route configuration validation
- Navigation component link rendering
- Authentication context redirect behavior

### Integration Testing
- End-to-end authentication flow
- Navigation between pages
- Direct URL access patterns

### User Acceptance Testing
- Verify improved user experience with descriptive route
- Confirm all functionality remains intact
- Test bookmark and direct link scenarios

## Security Considerations

### Route Protection
- Ensure `/create-ebook` route maintains same protection level as `/app`
- Verify ProtectedRoute component continues to function correctly
- Confirm authentication redirects work properly

### URL Structure
- New route structure is more descriptive and user-friendly
- Maintains RESTful conventions
- No security implications from route name change
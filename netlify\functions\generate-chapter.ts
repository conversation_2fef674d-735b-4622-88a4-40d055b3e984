import { Handler } from '@netlify/functions';
import { GoogleGenerativeAI } from "@google/generative-ai";

// Import shared types
import { ContentGenerationParams, BookLanguage } from "../../shared/types";

// Initialize the API with the API key
const API_KEY = process.env.GEMINI_API_KEY;
if (!API_KEY) {
  throw new Error("GEMINI_API_KEY environment variable is required");
}
const MODEL = "gemini-2.0-flash";

const genAI = new GoogleGenerativeAI(API_KEY);
const model = genAI.getGenerativeModel({ model: MODEL });

// Helper functions for tone/style descriptions
function getToneDescription(tone: string): string {
  const toneDescriptions = {
    professional: "formal, authoritative, and business-like",
    casual: "relaxed, conversational, and friendly",
    academic: "scholarly, rigorous, and evidence-based",
    conversational: "engaging, relatable, and approachable",
    instructional: "clear, directive, and educational"
  };
  return toneDescriptions[tone as keyof typeof toneDescriptions] || toneDescriptions.professional;
}

function getStyleDescription(style: string): string {
  const styleDescriptions = {
    descriptive: "rich in detail and imagery",
    analytical: "focused on breaking down concepts and examining relationships",
    persuasive: "aimed at convincing the reader with compelling arguments",
    narrative: "story-driven with clear flow and progression",
    technical: "precise, detailed, and specialized"
  };
  return styleDescriptions[style as keyof typeof styleDescriptions] || styleDescriptions.descriptive;
}

function getLanguageDescription(language: string): string {
  const languageDescriptions = {
    simple: "using straightforward vocabulary and short sentences accessible to a wide audience",
    intermediate: "balancing clarity with some field-specific terminology",
    advanced: "using sophisticated vocabulary and complex sentence structures",
    technical: "employing specialized terminology and jargon appropriate for experts"
  };
  return languageDescriptions[language as keyof typeof languageDescriptions] || languageDescriptions.intermediate;
}

function getTargetLanguageName(languageCode: BookLanguage): string {
  const languageNames: Record<BookLanguage, string> = {
    'ar': 'Arabic',
    'bn': 'Bengali',
    'bg': 'Bulgarian',
    'zh-CN': 'Chinese (Simplified)',
    'zh-TW': 'Chinese (Traditional)',
    'hr': 'Croatian',
    'cs': 'Czech',
    'da': 'Danish',
    'nl': 'Dutch',
    'en': 'English',
    'et': 'Estonian',
    'fi': 'Finnish',
    'fr': 'French',
    'de': 'German',
    'el': 'Greek',
    'he': 'Hebrew',
    'hi': 'Hindi',
    'hu': 'Hungarian',
    'id': 'Indonesian',
    'it': 'Italian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'no': 'Norwegian',
    'pl': 'Polish',
    'pt': 'Portuguese',
    'ro': 'Romanian',
    'ru': 'Russian',
    'sr': 'Serbian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'es': 'Spanish',
    'sw': 'Swahili',
    'sv': 'Swedish',
    'th': 'Thai',
    'tr': 'Turkish',
    'uk': 'Ukrainian',
    'vi': 'Vietnamese'
  };
  return languageNames[languageCode] || 'English';
}

async function generateChapterContent(
  subChapterTitle: string,
  mainChapterTitle: string,
  bookTopic: string,
  params: ContentGenerationParams
): Promise<string> {
  try {
    // Get descriptions for content generation
    const toneDescription = getToneDescription(params.tone);
    const styleDescription = getStyleDescription(params.style);
    const languageDescription = getLanguageDescription(params.language);
    const targetLanguage = params.targetLanguage ? getTargetLanguageName(params.targetLanguage) : "English";
    
    // Build audience context if target audience is specified
    const audienceContext = params.targetAudience && params.targetAudience !== "General Audience" 
      ? `\n\nTarget Audience: ${params.targetAudience}\n- Adapt examples, complexity, and tone for this specific audience\n- Use terminology and references relevant to their background and interests`
      : "";

    const prompt = `
    You are an expert non-fiction book writer for Amazon KDP. Write detailed, informative content for the following subchapter:

    Book Topic: "${bookTopic}"
    Main Chapter: "${mainChapterTitle}"
    Subchapter: "${subChapterTitle}"

    IMPORTANT: Write all content in ${targetLanguage}. The entire chapter content must be written in ${targetLanguage}.

    Writing Style:
    - Tone: ${toneDescription}
    - Style: ${styleDescription}
    - Language Level: ${languageDescription}${audienceContext}

    Guidelines:
    - Write approximately 800-1200 words of polished, publication-ready content
    - Include relevant headings where appropriate (using markdown format: # ## ###)
    - Include practical examples, actionable advice, and evidence-based information
    - Format the text with clear paragraphs, occasional bullet points for key points, and proper structure
    - Ensure this subchapter flows logically while being able to stand alone
    - Tailor examples and complexity level specifically for the target audience

    FORMATTING REQUIREMENTS:
    - DO NOT wrap the content in code blocks (no \`\`\`markdown or \`\`\`)
    - Use clean markdown formatting only
    - For bullet points, use "* " (asterisk followed by space) at the start of each line
    - Ensure each bullet point is on its own line
    - Use proper line breaks between sections
    - Do not use extra asterisks (*) or hash symbols (#) outside of proper markdown formatting

    Return only the clean subchapter content with proper markdown formatting. Do not include any code block markers.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    let text = response.text();

    // Clean up any remaining markdown artifacts
    text = text
      // Remove markdown code block markers
      .replace(/```markdown\s*/gi, '')
      .replace(/```\s*/g, '')
      // Remove extra asterisks that aren't part of formatting
      .replace(/\*{3,}/g, '')
      // Remove extra hash symbols that aren't headers
      .replace(/#{4,}/g, '###')
      // Clean up any double spaces
      .replace(/  +/g, ' ')
      // Ensure proper line breaks after bullet points
      .replace(/(\* [^\n]*)\s*(\* [^\n]*)/g, '$1\n\n$2')
      // Clean up excessive line breaks
      .replace(/\n{4,}/g, '\n\n\n')
      .trim();

    return text;
  } catch (error) {
    console.error("Error in generateChapterContent:", error);
    throw new Error("Failed to generate chapter content");
  }
}

export const handler: Handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle OPTIONS request for CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: 'Method Not Allowed' })
    };
  }

  try {
    const { 
      subChapterTitle, 
      mainChapterTitle, 
      bookTopic, 
      generationParams 
    } = JSON.parse(event.body || '{}');
    
    if (!subChapterTitle || !mainChapterTitle || !bookTopic) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          message: "Missing required fields. Please provide subChapterTitle, mainChapterTitle, and bookTopic." 
        })
      };
    }

    // Default parameters if not provided
    const params: ContentGenerationParams = generationParams || {
      tone: 'professional',
      style: 'descriptive',
      language: 'intermediate'
    };

    const subChapterContent = await generateChapterContent(
      subChapterTitle,
      mainChapterTitle,
      bookTopic,
      params
    );
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ subChapterContent })
    };
  } catch (error) {
    console.error("Error generating chapter content:", error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        message: "Failed to generate chapter content. Please try again." 
      })
    };
  }
};
# Book Title Selection Workflow Design

## Overview

This design document outlines the implementation of a two-step book generation workflow in the NonFictionBookBuilder application. The workflow transforms the current single-step outline generation into an enhanced user experience where users first select from AI-generated book titles, then generate detailed outlines based on their selection.

## Architecture

### Current State Analysis

The existing system uses a direct approach:
- User enters book topic → Generate Outline → Content Generation

### Target State Architecture

The enhanced workflow introduces an intermediate step:
- User enters book topic → Generate Book Titles → Select Title → Generate Outline → Content Generation

```mermaid
graph TD
    A[User Input: Book Topic] --> B[Generate Book Titles]
    B --> C[Display 10 Book Titles]
    C --> D[User Selects Title]
    D --> E[Generate Outline for Selected Title]
    E --> F[Display Chapter/Sub-chapter Structure]
    F --> G[Sequential Content Generation]
```

### Component Interaction Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant OG as OutlineGenerator
    participant API as Backend API
    participant CG as ContentGenerator
    
    U->>OG: Enter book topic
    U->>OG: Click "Generate Book Titles"
    OG->>API: POST /api/generate-titles
    API-->>OG: Return 10 book titles
    OG->>OG: Display titles in right panel
    U->>OG: Select book title
    OG->>OG: Highlight selected title
    OG->>OG: Show "Generate Outline" button
    U->>OG: Click "Generate Outline"
    OG->>API: POST /api/generate-outline (with selected title)
    API-->>OG: Return outline structure
    OG->>CG: Display outline for content generation
```

## Data Models & State Management

### New Type Definitions

```typescript
// Add to shared/types.ts
export interface BookTitle {
  id: string;
  title: string;
  description?: string;
}

export interface BookTitlesResponse {
  titles: BookTitle[];
}

export interface TitleSelectionState {
  generatedTitles: BookTitle[];
  selectedTitle: BookTitle | null;
  isGeneratingTitles: boolean;
  titlesError: string | null;
}
```

### State Management Updates

#### OutlineGenerator Component State
```typescript
// New state additions
const [titleState, setTitleState] = useState<TitleSelectionState>({
  generatedTitles: [],
  selectedTitle: null,
  isGeneratingTitles: false,
  titlesError: null
});

const [workflowStep, setWorkflowStep] = useState<'input' | 'title-selection' | 'outline-generated'>('input');
```

#### AppPage Component State
```typescript
// Add to existing state
const [selectedBookTitle, setSelectedBookTitle] = useState<BookTitle | null>(null);
const [generatedTitles, setGeneratedTitles] = useState<BookTitle[]>([]);
```

## API Endpoints

### New Book Titles Generation Endpoint

```typescript
// Add to server/routes.ts
app.post("/api/generate-titles", async (req, res) => {
  try {
    const { userInput, generationParams } = req.body;
    
    if (!userInput || typeof userInput !== "string") {
      return res.status(400).json({ 
        message: "Invalid input. Please provide a valid 'userInput' string." 
      });
    }

    const titles = await generateBookTitles(userInput, generationParams);
    return res.json({ titles });
  } catch (error) {
    console.error("Error generating book titles:", error);
    return res.status(500).json({ 
      message: "Failed to generate book titles. Please try again." 
    });
  }
});
```

### Modified Outline Generation Endpoint

```typescript
// Update existing endpoint to accept selected title
app.post("/api/generate-outline", async (req, res) => {
  try {
    const { selectedTitle, userInput, generationParams } = req.body;
    
    // Use selected title as primary input, fallback to userInput
    const bookTitle = selectedTitle?.title || userInput;
    
    if (!bookTitle || typeof bookTitle !== "string") {
      return res.status(400).json({ 
        message: "Invalid input. Please provide a selected title or user input." 
      });
    }

    const outline = await generateOutline(bookTitle, generationParams);
    return res.json({ outline });
  } catch (error) {
    console.error("Error generating outline:", error);
    return res.status(500).json({ 
      message: "Failed to generate outline. Please try again." 
    });
  }
});
```

## Backend Implementation

### Gemini API Integration for Title Generation

```typescript
// Add to server/gemini.ts
export async function generateBookTitles(
  userInput: string,
  params?: OutlineGenerationParams
): Promise<BookTitle[]> {
  try {
    const toneDescription = params?.tone ? getToneDescription(params.tone) : "";
    const styleDescription = params?.style ? getStyleDescription(params.style) : "";

    const prompt = `
    You are an expert book title generator for non-fiction Amazon KDP books. 
    Generate 10 engaging, compelling, and marketable book titles based on the following topic: "${userInput}"

    Requirements:
    - Titles should be attention-grabbing and professional
    - Each title should be 3-8 words long for optimal readability
    - Include power words that indicate value and transformation
    - Make titles specific and actionable
    - Ensure titles are unique and diverse in approach
    ${params?.tone ? `- Use a ${toneDescription} tone` : ""}
    ${params?.style ? `- Write in a ${styleDescription} style` : ""}

    Format your response as a JSON array of objects with this structure:
    [
      {
        "id": "1",
        "title": "Book Title Here",
        "description": "Brief description of what this title offers"
      }
    ]

    Only return the JSON array, no additional text.
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error("Failed to generate valid JSON titles");
    }

    const parsedTitles = JSON.parse(jsonMatch[0]);
    
    if (!Array.isArray(parsedTitles) || parsedTitles.length === 0) {
      throw new Error("Invalid titles structure");
    }

    return parsedTitles;
  } catch (error) {
    console.error("Error in generateBookTitles:", error);
    throw new Error("Failed to generate book titles");
  }
}
```

## Frontend Implementation

### OutlineGenerator Component Updates

#### Button State Management
```typescript
const renderMainActionButton = () => {
  if (workflowStep === 'input') {
    return (
      <Button 
        className="w-full" 
        onClick={generateTitles}
        disabled={titleState.isGeneratingTitles || !bookTopic?.trim()}
      >
        {titleState.isGeneratingTitles ? "Generating Titles..." : "Generate Book Titles"}
      </Button>
    );
  } else if (workflowStep === 'title-selection') {
    return (
      <Button 
        className="w-full" 
        onClick={generateOutline}
        disabled={isLoading || !titleState.selectedTitle}
        variant={titleState.selectedTitle ? "default" : "outline"}
      >
        {isLoading ? "Generating Outline..." : "Generate Outline"}
      </Button>
    );
  }
  
  return null;
};
```

#### Title Generation Logic
```typescript
const generateTitles = async () => {
  if (!bookTopic?.trim()) {
    toast({
      title: "Input required",
      description: "Please enter a book topic or keywords",
      variant: "destructive"
    });
    return;
  }

  setTitleState(prev => ({ ...prev, isGeneratingTitles: true, titlesError: null }));

  try {
    const res = await apiRequest('POST', '/api/generate-titles', {
      userInput: bookTopic,
      generationParams: outlineParams
    });
    const data = await res.json();
    
    setTitleState(prev => ({
      ...prev,
      generatedTitles: data.titles,
      isGeneratingTitles: false
    }));
    
    setWorkflowStep('title-selection');
    
    toast({
      title: "Titles generated",
      description: "Select a book title to generate the outline",
    });
  } catch (err) {
    console.error("Error generating titles:", err);
    setTitleState(prev => ({
      ...prev,
      titlesError: "Failed to generate titles. Please try again.",
      isGeneratingTitles: false
    }));
    
    toast({
      title: "Generation failed",
      description: "Could not generate book titles. Please try again.",
      variant: "destructive"
    });
  }
};
```

#### Title Selection Handler
```typescript
const handleTitleSelect = (title: BookTitle) => {
  setTitleState(prev => ({
    ...prev,
    selectedTitle: title
  }));
  
  // Notify parent component about the selection
  onTitleSelected?.(title);
};
```

### ContentGenerator Component Updates

#### Title Display Integration
```typescript
const TitleSelectionPanel = () => {
  if (workflowStep !== 'title-selection' || titleState.generatedTitles.length === 0) {
    return null;
  }

  return (
    <div className="p-6 space-y-4">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold mb-2">Select Your Book Title</h2>
        <p className="text-muted-foreground">Choose the most compelling title for your book</p>
      </div>
      
      <div className="space-y-3">
        {titleState.generatedTitles.map((title) => (
          <div
            key={title.id}
            className={`
              p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 
              hover:shadow-md hover:scale-[1.02]
              ${titleState.selectedTitle?.id === title.id 
                ? 'border-primary bg-primary/10 shadow-lg' 
                : 'border-muted hover:border-primary/50 bg-card'
              }
            `}
            onClick={() => handleTitleSelect(title)}
          >
            <h3 className={`font-semibold text-lg mb-2 ${
              titleState.selectedTitle?.id === title.id ? 'text-primary' : 'text-foreground'
            }`}>
              {title.title}
            </h3>
            {title.description && (
              <p className="text-sm text-muted-foreground">{title.description}</p>
            )}
            {titleState.selectedTitle?.id === title.id && (
              <div className="mt-3 flex items-center text-primary">
                <CheckIcon className="h-4 w-4 mr-1" />
                <span className="text-xs font-medium">Selected</span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Right Panel State Management

#### ContentGenerator Empty State Update
```typescript
const EmptyState = () => {
  if (workflowStep === 'title-selection') {
    return <TitleSelectionPanel />;
  }
  
  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center">
        <svg className="h-16 w-16 text-muted-foreground mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 className="text-lg font-medium mb-2">No Content Selected</h3>
        <p className="text-muted-foreground">
          {workflowStep === 'input' 
            ? "Enter your book topic and generate titles to get started" 
            : "Generate an outline and select a chapter to view content"
          }
        </p>
      </div>
    </div>
  );
};
```

## UI/UX Design Specifications

### Visual Design Guidelines

#### Title Selection Cards
- **Unselected State**: 
  - Border: `border-muted`
  - Background: `bg-card`
  - Hover: `hover:border-primary/50 hover:shadow-md hover:scale-[1.02]`

- **Selected State**:
  - Border: `border-primary` (2px)
  - Background: `bg-primary/10`
  - Shadow: `shadow-lg`
  - Text Color: `text-primary` for title
  - Check Icon: Visible with primary color

#### Button States
- **"Generate Book Titles"**: Primary button, full width
- **"Generate Outline"**: 
  - Disabled until title selected (outline variant)
  - Primary when title selected

#### Transition Animations
```css
/* Title card hover effect */
.title-card {
  transition: all 0.2s ease-in-out;
}

.title-card:hover {
  transform: scale(1.02);
}

/* Step transition */
.workflow-step {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
```

### Responsive Design Considerations

#### Mobile Layout (< 768px)
- Stack title cards vertically with full width
- Reduce padding and font sizes
- Maintain touch-friendly tap targets (minimum 44px height)

#### Tablet Layout (768px - 1024px)
- Two-column grid for title cards
- Adjust spacing for optimal readability

#### Desktop Layout (> 1024px)
- Three-column grid for title cards
- Enhanced hover effects
- Larger typography for better scanning

## Error Handling & Edge Cases

### Title Generation Failures
```typescript
const handleTitleGenerationError = (error: Error) => {
  setTitleState(prev => ({
    ...prev,
    titlesError: "Failed to generate titles. Please try again.",
    isGeneratingTitles: false
  }));

  toast({
    title: "Generation failed",
    description: "Could not generate book titles. Please check your connection and try again.",
    variant: "destructive"
  });
};
```

### Network Connectivity Issues
- Implement retry mechanism for API calls
- Show appropriate loading states
- Graceful degradation with cached data

### Invalid API Responses
```typescript
const validateTitlesResponse = (data: any): BookTitle[] => {
  if (!Array.isArray(data.titles)) {
    throw new Error("Invalid response format");
  }
  
  return data.titles.filter((title: any) => 
    title.id && title.title && typeof title.title === 'string'
  );
};
```

## Testing Strategy

### Unit Testing

#### Component Testing
```typescript
describe('OutlineGenerator Title Selection', () => {
  it('should display Generate Book Titles button initially', () => {
    render(<OutlineGenerator {...defaultProps} />);
    expect(screen.getByText('Generate Book Titles')).toBeInTheDocument();
  });

  it('should show title selection after successful generation', async () => {
    // Mock API response
    // Render component
    // Trigger title generation
    // Assert title cards are displayed
  });

  it('should highlight selected title', () => {
    // Render with generated titles
    // Click on a title
    // Assert selected state styles
  });
});
```

#### API Testing
```typescript
describe('Title Generation API', () => {
  it('should return 10 book titles', async () => {
    const mockInput = "Digital Marketing Strategy";
    const result = await generateBookTitles(mockInput);
    
    expect(result).toHaveLength(10);
    expect(result[0]).toHaveProperty('id');
    expect(result[0]).toHaveProperty('title');
  });
});
```

### Integration Testing

#### Workflow Testing
- Test complete user journey from input to outline generation
- Verify state management across components
- Test error recovery scenarios

### User Acceptance Testing

#### Success Criteria
1. User can generate book titles from topic input
2. User can select and highlight a title
3. Outline generation works with selected title
4. UI provides clear visual feedback at each step
5. Error states are handled gracefully

## Performance Considerations

### API Response Optimization
- Implement response caching for generated titles
- Use debouncing for user input to prevent excessive API calls
- Optimize Gemini API prompts for faster response times

### Frontend Performance
```typescript
// Memoize expensive calculations
const memoizedTitles = useMemo(() => 
  titleState.generatedTitles.map(title => ({
    ...title,
    displayTitle: formatTitleForDisplay(title.title)
  })), 
  [titleState.generatedTitles]
);

// Lazy load components
const TitleSelectionPanel = lazy(() => import('./TitleSelectionPanel'));
```

### State Management Optimization
- Use context selectors to prevent unnecessary re-renders
- Implement proper dependency arrays in useEffect hooks
- Consider using state machines for complex workflow state

## Security Considerations

### Input Validation
```typescript
const validateBookTopic = (input: string): boolean => {
  const sanitized = input.trim();
  return sanitized.length >= 3 && sanitized.length <= 200;
};
```

### API Security
- Implement rate limiting for title generation endpoint
- Sanitize user inputs before sending to Gemini API
- Add request validation middleware

### Data Privacy
- Clear generated titles from state when user starts new session
- Implement session timeout for sensitive operations
- Ensure no sensitive data in localStorage
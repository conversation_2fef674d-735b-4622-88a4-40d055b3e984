import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 shadow-md hover:shadow-lg hover:translate-y-[-1px] border border-transparent",
        destructive:
          "bg-gradient-to-r from-red-500 to-rose-600 text-white hover:from-red-600 hover:to-rose-700 shadow-md hover:shadow-lg hover:translate-y-[-1px] border border-transparent",
        outline:
          "border border-glass bg-glass-card backdrop-blur-sm text-white hover:bg-gradient-to-r hover:from-gray-500/20 hover:to-gray-600/20 hover:text-white shadow-glass",
        secondary:
          "bg-gradient-to-r from-purple-500 to-violet-600 text-white hover:from-purple-600 hover:to-violet-700 shadow-md hover:shadow-lg hover:translate-y-[-1px] border border-transparent",
        ghost: "text-white hover:bg-gradient-to-r hover:from-gray-700/20 hover:to-gray-800/20 hover:text-white",
        link: "text-white font-medium underline-offset-4 hover:underline hover:text-blue-300",
        // New variants for Generate All Sub-Chapters button states
        generating: "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-md border border-transparent backdrop-blur-sm transition-colors duration-300 ease-in-out",
        generated: "bg-gradient-to-r from-green-500 to-green-600 text-white shadow-md border border-transparent backdrop-blur-sm transition-colors duration-300 ease-in-out",
        error: "bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-md border border-transparent backdrop-blur-sm transition-colors duration-300 ease-in-out",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-6 py-3 text-base",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

import React from 'react';
import { BookSize } from '@shared/types';
import { getBookSizeLabel, getBookSizeDescription } from '@shared/bookSizeUtils';

interface BookSizeSelectorProps {
  selectedSize: BookSize;
  onSizeChange: (size: BookSize) => void;
  disabled?: boolean;
}

export default function BookSizeSelector({
  selectedSize,
  onSizeChange,
  disabled = false
}: BookSizeSelectorProps) {
  const sizes: BookSize[] = ['small', 'medium', 'large'];

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-muted-foreground mb-3">
        Book Size
      </label>
      
      <div className="grid grid-cols-1 gap-3">
        {sizes.map((size) => (
          <label
            key={size}
            className={`
              relative flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all duration-200
              ${selectedSize === size
                ? 'border-primary bg-gradient-to-r from-primary/10 to-primary/5 shadow-md'
                : 'border-muted hover:border-primary/50 bg-card hover:bg-card/80'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}
            `}
          >
            <input
              type="radio"
              name="bookSize"
              value={size}
              checked={selectedSize === size}
              onChange={(e) => onSizeChange(e.target.value as BookSize)}
              disabled={disabled}
              className="sr-only"
            />
            
            {/* Custom radio button */}
            <div className={`
              flex-shrink-0 w-5 h-5 rounded-full border-2 mr-3 transition-all duration-200
              ${selectedSize === size
                ? 'border-primary bg-primary'
                : 'border-muted-foreground/30'
              }
            `}>
              {selectedSize === size && (
                <div className="w-full h-full rounded-full bg-white scale-50 transition-transform duration-200" />
              )}
            </div>
            
            {/* Size info */}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <span className={`
                  font-medium text-base
                  ${selectedSize === size ? 'text-primary' : 'text-foreground'}
                `}>
                  {getBookSizeLabel(size)}
                </span>
                
                {selectedSize === size && (
                  <div className="flex items-center text-primary">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                )}
              </div>
              
              <p className="text-sm text-muted-foreground mt-1">
                {getBookSizeDescription(size)}
              </p>
            </div>
          </label>
        ))}
      </div>
      
      {/* Selected size summary */}
      <div className="mt-3 p-3 bg-muted/30 rounded-lg">
        <div className="flex items-center text-sm">
          <svg className="w-4 h-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span className="text-muted-foreground">
            Selected: <span className="font-medium text-foreground">{getBookSizeLabel(selectedSize)}</span> - {getBookSizeDescription(selectedSize)}
          </span>
        </div>
      </div>
    </div>
  );
}

import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./client/index.html", "./client/src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      backdropBlur: {
        xs: '2px',
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
        '2xl': '24px',
        '3xl': '40px',
      },
      backgroundColor: {
        'glass': 'rgba(255, 255, 255, 0.1)',
        'glass-dark': 'rgba(0, 0, 0, 0.2)',
        'glass-card': 'rgba(255, 255, 255, 0.05)',
        'glass-spinner': 'rgba(255, 255, 255, 0.03)',
        'glass-skeleton': 'rgba(255, 255, 255, 0.08)',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(to right, var(--tw-gradient-stops))',
        'gradient-primary-hover': 'linear-gradient(to right, #2563eb, #4338ca)',
        'gradient-secondary': 'linear-gradient(to right, #a855f7, #8b5cf6)',
        'gradient-secondary-hover': 'linear-gradient(to right, #9333ea, #7e22ce)',
        'gradient-destructive': 'linear-gradient(to right, #ef4444, #e11d48)',
        'gradient-destructive-hover': 'linear-gradient(to right, #dc2626, #be123c)',
        'gradient-success': 'linear-gradient(to right, #10b981, #059669)',
        'gradient-success-hover': 'linear-gradient(to right, #059669, #047857)',
        'shimmer-gradient': 'linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0px, rgba(255, 255, 255, 0.15) 40px, rgba(255, 255, 255, 0.05) 80px)',
        'glass-radial': 'radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
      },
      gradientColorStops: {
        'blue-start': '#3b82f6',
        'blue-end': '#4f46e5',
        'purple-start': '#a855f7',
        'purple-end': '#8b5cf6',
        'red-start': '#ef4444',
        'red-end': '#e11d48',
        'green-start': '#10b981',
        'green-end': '#059669',
      },
      borderColor: {
        'glass': 'rgba(255, 255, 255, 0.2)',
        'glass-subtle': 'rgba(255, 255, 255, 0.1)',
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glass-lg': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "glass-spin": {
          "0%": {
            transform: "rotate(0deg) scale(1)",
            boxShadow: "0 0 20px rgba(59, 130, 246, 0.3)",
          },
          "50%": {
            transform: "rotate(180deg) scale(1.05)",
            boxShadow: "0 0 30px rgba(59, 130, 246, 0.5)",
          },
          "100%": {
            transform: "rotate(360deg) scale(1)",
            boxShadow: "0 0 20px rgba(59, 130, 246, 0.3)",
          },
        },
        "shimmer": {
          "0%": {
            backgroundPosition: "-200px 0",
          },
          "100%": {
            backgroundPosition: "calc(200px + 100%) 0",
          },
        },
        "typewriter": {
          "from": {
            width: "0",
          },
          "to": {
            width: "100%",
          },
        },
        "blink": {
          "50%": {
            borderColor: "transparent",
          },
        },
        "pulse-glow": {
          "0%, 100%": {
            opacity: "1",
            boxShadow: "0 0 15px rgba(59, 130, 246, 0.4)",
          },
          "50%": {
            opacity: "0.8",
            boxShadow: "0 0 25px rgba(59, 130, 246, 0.6)",
          },
        },
        "fade-in": {
          "from": {
            opacity: "0",
            transform: "translateY(10px)",
          },
          "to": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "scale-bounce": {
          "0%, 100%": {
            transform: "scale(1)",
          },
          "50%": {
            transform: "scale(1.05)",
          },
        },
        "ripple": {
          "0%": {
            transform: "scale(0)",
            opacity: "1",
          },
          "100%": {
            transform: "scale(4)",
            opacity: "0",
          },
        },
        "particle-float": {
          "0%, 100%": {
            transform: "translateY(0) translateX(0) rotate(0deg)",
          },
          "33%": {
            transform: "translateY(-10px) translateX(5px) rotate(120deg)",
          },
          "66%": {
            transform: "translateY(5px) translateX(-5px) rotate(240deg)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "glass-spin": "glass-spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite",
        "shimmer": "shimmer 2s infinite ease-in-out",
        "typewriter": "typewriter 3s steps(40, end)",
        "blink": "blink 1s step-end infinite",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite",
        "fade-in": "fade-in 0.3s ease-out",
        "scale-bounce": "scale-bounce 2s ease-in-out infinite",
        "ripple": "ripple 0.6s ease-out",
        "particle-float": "particle-float 3s ease-in-out infinite",
        "spin-smooth": "spin 1s linear infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
} satisfies Config;

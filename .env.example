# Environment Configuration Guide

## Quick Setup

Run: `npm run setup` to automatically create your .env file, then edit it with your API keys.

Or manually:

## Local Development Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Add your API keys to `.env`:**
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   NODE_ENV=development
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

## Production Deployment

### For Netlify Deployment:

**⚠️ IMPORTANT:** Never commit `.env` files to git for security reasons.

1. **Set environment variables in Netlify Dashboard:**
   - Go to: https://app.netlify.com/projects/aiebookwriterpro/settings/environment-variables
   - Add: `GEMINI_API_KEY` with your actual API key value

2. **Deploy:**
   ```bash
   netlify deploy --prod
   ```

### For Other Hosting Platforms:

- **Vercel:** Add environment variables in project settings
- **Railway:** Set environment variables in dashboard
- **Heroku:** Use `heroku config:set GEMINI_API_KEY=your_key`

## Security Best Practices

1. **Never commit API keys to source control**
2. **Use different API keys for development and production**
3. **Regularly rotate API keys**
4. **Monitor API usage for unusual activity**

## Environment Variables Reference

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `GEMINI_API_KEY` | Google Gemini AI API key | Yes | None |
| `NODE_ENV` | Application environment | No | development |